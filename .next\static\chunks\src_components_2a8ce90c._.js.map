{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/three/AIParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useMemo } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Particle system component\nfunction ParticleField({ count = 1000 }) {\n  const mesh = useRef();\n  const light = useRef();\n\n  // Generate random positions for particles\n  const particles = useMemo(() => {\n    const temp = new Float32Array(count * 3);\n    for (let i = 0; i < count; i++) {\n      const i3 = i * 3;\n      temp[i3] = (Math.random() - 0.5) * 20;\n      temp[i3 + 1] = (Math.random() - 0.5) * 20;\n      temp[i3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return temp;\n  }, [count]);\n\n  // Animation loop\n  useFrame((state) => {\n    const time = state.clock.getElapsedTime();\n    \n    if (mesh.current) {\n      mesh.current.rotation.x = time * 0.1;\n      mesh.current.rotation.y = time * 0.05;\n      \n      // Update particle positions for floating effect\n      const positions = mesh.current.geometry.attributes.position.array;\n      for (let i = 0; i < positions.length; i += 3) {\n        positions[i + 1] += Math.sin(time + positions[i]) * 0.001;\n      }\n      mesh.current.geometry.attributes.position.needsUpdate = true;\n    }\n\n    if (light.current) {\n      light.current.position.x = Math.sin(time) * 5;\n      light.current.position.z = Math.cos(time) * 5;\n    }\n  });\n\n  return (\n    <group>\n      <Points ref={mesh} positions={particles} stride={3} frustumCulled={false}>\n        <PointMaterial\n          transparent\n          color=\"#00D4FF\"\n          size={0.05}\n          sizeAttenuation={true}\n          depthWrite={false}\n          blending={THREE.AdditiveBlending}\n        />\n      </Points>\n      \n      {/* AI Network Lines */}\n      <mesh>\n        <sphereGeometry args={[8, 32, 32]} />\n        <meshBasicMaterial\n          color=\"#8B5CF6\"\n          transparent\n          opacity={0.1}\n          wireframe\n        />\n      </mesh>\n\n      {/* Floating light */}\n      <pointLight\n        ref={light}\n        color=\"#00FF88\"\n        intensity={0.5}\n        distance={10}\n        decay={2}\n      />\n    </group>\n  );\n}\n\n// Neural network connections\nfunction NeuralNetwork() {\n  const lines = useRef();\n\n  useFrame((state) => {\n    if (lines.current) {\n      lines.current.rotation.y = state.clock.getElapsedTime() * 0.1;\n    }\n  });\n\n  const connections = useMemo(() => {\n    const points = [];\n    const nodeCount = 20;\n    \n    // Create nodes in 3D space\n    const nodes = [];\n    for (let i = 0; i < nodeCount; i++) {\n      nodes.push(new THREE.Vector3(\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15\n      ));\n    }\n\n    // Connect nearby nodes\n    for (let i = 0; i < nodes.length; i++) {\n      for (let j = i + 1; j < nodes.length; j++) {\n        if (nodes[i].distanceTo(nodes[j]) < 5) {\n          points.push(nodes[i], nodes[j]);\n        }\n      }\n    }\n\n    return points;\n  }, []);\n\n  return (\n    <group ref={lines}>\n      <lineSegments>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={connections.length}\n            array={new Float32Array(connections.flatMap(v => [v.x, v.y, v.z]))}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <lineBasicMaterial\n          color=\"#00D4FF\"\n          transparent\n          opacity={0.3}\n          blending={THREE.AdditiveBlending}\n        />\n      </lineSegments>\n    </group>\n  );\n}\n\n// Main background component\nconst AIParticleBackground = () => {\n  return (\n    <div className=\"absolute inset-0 w-full h-full\">\n      <Canvas\n        camera={{ position: [0, 0, 10], fov: 60 }}\n        style={{ background: 'transparent' }}\n      >\n        <ambientLight intensity={0.2} />\n        <ParticleField count={1500} />\n        <NeuralNetwork />\n        \n        {/* Fog for depth */}\n        <fog attach=\"fog\" args={['#0A0A0F', 10, 50]} />\n      </Canvas>\n    </div>\n  );\n};\n\nexport default AIParticleBackground;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,4BAA4B;AAC5B,SAAS,cAAc,EAAE,QAAQ,IAAI,EAAE;;IACrC,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAClB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEnB,0CAA0C;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACxB,MAAM,OAAO,IAAI,aAAa,QAAQ;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,KAAK,IAAI;gBACf,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACvC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC;YACA,OAAO;QACT;2CAAG;QAAC;KAAM;IAEV,iBAAiB;IACjB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,MAAM,OAAO,MAAM,KAAK,CAAC,cAAc;YAEvC,IAAI,KAAK,OAAO,EAAE;gBAChB,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;gBACjC,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;gBAEjC,gDAAgD;gBAChD,MAAM,YAAY,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK;gBACjE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;oBAC5C,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,SAAS,CAAC,EAAE,IAAI;gBACtD;gBACA,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;YAC1D;YAEA,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;gBAC5C,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;YAC9C;QACF;;IAEA,qBACE,6LAAC;;0BACC,6LAAC,6JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAM,WAAW;gBAAW,QAAQ;gBAAG,eAAe;0BACjE,cAAA,6LAAC,oKAAA,CAAA,gBAAa;oBACZ,WAAW;oBACX,OAAM;oBACN,MAAM;oBACN,iBAAiB;oBACjB,YAAY;oBACZ,UAAU,kJAAA,CAAA,mBAAsB;;;;;;;;;;;0BAKpC,6LAAC;;kCACC,6LAAC;wBAAe,MAAM;4BAAC;4BAAG;4BAAI;yBAAG;;;;;;kCACjC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,SAAS;;;;;;;;;;;;0BAKb,6LAAC;gBACC,KAAK;gBACL,OAAM;gBACN,WAAW;gBACX,UAAU;gBACV,OAAO;;;;;;;;;;;;AAIf;GAxES;;QAiBP,kNAAA,CAAA,WAAQ;;;KAjBD;AA0ET,6BAA6B;AAC7B,SAAS;;IACP,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEnB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,cAAc,KAAK;YAC5D;QACF;;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC1B,MAAM,SAAS,EAAE;YACjB,MAAM,YAAY;YAElB,2BAA2B;YAC3B,MAAM,QAAQ,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,MAAM,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAC1B,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE5B;YAEA,uBAAuB;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACzC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG;wBACrC,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;oBAChC;gBACF;YACF;YAEA,OAAO;QACT;6CAAG,EAAE;IAEL,qBACE,6LAAC;QAAM,KAAK;kBACV,cAAA,6LAAC;;8BACC,6LAAC;8BACC,cAAA,6LAAC;wBACC,QAAO;wBACP,OAAO,YAAY,MAAM;wBACzB,OAAO,IAAI,aAAa,YAAY,OAAO,CAAC,CAAA,IAAK;gCAAC,EAAE,CAAC;gCAAE,EAAE,CAAC;gCAAE,EAAE,CAAC;6BAAC;wBAChE,UAAU;;;;;;;;;;;8BAGd,6LAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,UAAU,kJAAA,CAAA,mBAAsB;;;;;;;;;;;;;;;;;AAK1C;IAvDS;;QAGP,kNAAA,CAAA,WAAQ;;;MAHD;AAyDT,4BAA4B;AAC5B,MAAM,uBAAuB;IAC3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAG;gBAAE,KAAK;YAAG;YACxC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,6LAAC;oBAAa,WAAW;;;;;;8BACzB,6LAAC;oBAAc,OAAO;;;;;;8BACtB,6LAAC;;;;;8BAGD,6LAAC;oBAAI,QAAO;oBAAM,MAAM;wBAAC;wBAAW;wBAAI;qBAAG;;;;;;;;;;;;;;;;;AAInD;MAhBM;uCAkBS", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/AITerminal.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Terminal, Play, Zap, FileText, Camera, Bell } from 'lucide-react';\n\nconst AITerminal = () => {\n  const [currentCommand, setCurrentCommand] = useState('');\n  const [output, setOutput] = useState([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showCursor, setShowCursor] = useState(true);\n  const terminalRef = useRef(null);\n\n  const commands = [\n    {\n      command: 'scan receipt.jpg',\n      output: [\n        'Analyzing receipt...',\n        'Product: iPhone 15 Pro',\n        'Brand: Apple',\n        'Purchase Date: 2024-01-15',\n        'Warranty: 1 year (expires 2025-01-15)',\n        '✓ Added to warranty tracker'\n      ],\n      icon: <Camera className=\"w-4 h-4\" />\n    },\n    {\n      command: 'check warranties',\n      output: [\n        'Scanning warranty database...',\n        '📱 iPhone 15 Pro - 11 months remaining',\n        '💻 MacBook Pro - 8 months remaining',\n        '🏠 Samsung Fridge - 2 years remaining',\n        '⚠️  3 items expiring in 30 days'\n      ],\n      icon: <FileText className=\"w-4 h-4\" />\n    },\n    {\n      command: 'set reminder AC filter',\n      output: [\n        'Creating smart reminder...',\n        'Reminder: Replace AC filter',\n        'Frequency: Every 3 months',\n        'Next due: March 15, 2024',\n        '✓ Reminder activated'\n      ],\n      icon: <Bell className=\"w-4 h-4\" />\n    }\n  ];\n\n  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);\n\n  // Cursor blinking effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Auto-demo cycle\n  useEffect(() => {\n    const runDemo = async () => {\n      if (currentCommandIndex >= commands.length) {\n        // Reset after all commands\n        setTimeout(() => {\n          setOutput([]);\n          setCurrentCommandIndex(0);\n        }, 3000);\n        return;\n      }\n\n      const cmd = commands[currentCommandIndex];\n      setIsTyping(true);\n      \n      // Type command\n      for (let i = 0; i <= cmd.command.length; i++) {\n        setCurrentCommand(cmd.command.slice(0, i));\n        await new Promise(resolve => setTimeout(resolve, 50));\n      }\n\n      // Execute command\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setCurrentCommand('');\n      \n      // Show output line by line\n      for (let i = 0; i < cmd.output.length; i++) {\n        await new Promise(resolve => setTimeout(resolve, 300));\n        setOutput(prev => [...prev, {\n          type: 'output',\n          text: cmd.output[i],\n          timestamp: Date.now()\n        }]);\n      }\n\n      setIsTyping(false);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setCurrentCommandIndex(prev => prev + 1);\n    };\n\n    const timer = setTimeout(runDemo, 1000);\n    return () => clearTimeout(timer);\n  }, [currentCommandIndex]);\n\n  const handleCommandClick = (cmdIndex) => {\n    setCurrentCommandIndex(cmdIndex);\n    setOutput([]);\n  };\n\n  return (\n    <div className=\"relative w-full max-w-4xl mx-auto\">\n      {/* Terminal Window */}\n      <div className=\"glass rounded-lg overflow-hidden border border-primary/20 shadow-2xl\">\n        {/* Terminal Header */}\n        <div className=\"flex items-center justify-between px-4 py-3 bg-surface/50 border-b border-primary/20\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex space-x-2\">\n              <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n            </div>\n            <Terminal className=\"w-4 h-4 text-primary ml-4\" />\n            <span className=\"text-sm font-mono text-white/80\">WarrantyAI Terminal v2.0</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 rounded-full bg-accent animate-pulse\"></div>\n            <span className=\"text-xs text-accent font-mono\">ONLINE</span>\n          </div>\n        </div>\n\n        {/* Terminal Content */}\n        <div \n          ref={terminalRef}\n          className=\"p-6 h-80 overflow-y-auto font-mono text-sm bg-gradient-to-b from-space/80 to-surface/80\"\n        >\n          {/* Welcome Message */}\n          <div className=\"text-accent mb-4\">\n            <div>Welcome to WarrantyAI Terminal</div>\n            <div className=\"text-white/60\">Type commands or click suggestions below</div>\n            <div className=\"text-primary\">{'>'} Ready for input...</div>\n          </div>\n\n          {/* Output History */}\n          {output.map((line, index) => (\n            <div key={index} className=\"mb-1\">\n              {line.type === 'output' && (\n                <div className=\"text-white/80 pl-4\">{line.text}</div>\n              )}\n            </div>\n          ))}\n\n          {/* Current Command Line */}\n          <div className=\"flex items-center text-primary\">\n            <span className=\"mr-2\">{'>'}</span>\n            <span>{currentCommand}</span>\n            {showCursor && <span className=\"bg-primary w-2 h-4 ml-1 animate-pulse\">|</span>}\n          </div>\n        </div>\n\n        {/* Command Suggestions */}\n        <div className=\"p-4 bg-surface/30 border-t border-primary/20\">\n          <div className=\"text-xs text-white/60 mb-2\">Quick Commands:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {commands.map((cmd, index) => (\n              <button\n                key={index}\n                onClick={() => handleCommandClick(index)}\n                className=\"flex items-center space-x-2 px-3 py-1 bg-primary/10 hover:bg-primary/20 rounded border border-primary/30 transition-all duration-300 text-xs text-white/80 hover:text-white\"\n              >\n                {cmd.icon}\n                <span>{cmd.command}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Buttons */}\n      <div className=\"absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-4\">\n        <button className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Play className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n        <button className=\"w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center shadow-lg hover:shadow-accent/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Zap className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AITerminal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,WAAW;QACf;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW;iDAAY;oBAC3B;yDAAc,CAAA,OAAQ,CAAC;;gBACzB;gDAAG;YACH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;gDAAU;oBACd,IAAI,uBAAuB,SAAS,MAAM,EAAE;wBAC1C,2BAA2B;wBAC3B;4DAAW;gCACT,UAAU,EAAE;gCACZ,uBAAuB;4BACzB;2DAAG;wBACH;oBACF;oBAEA,MAAM,MAAM,QAAQ,CAAC,oBAAoB;oBACzC,YAAY;oBAEZ,eAAe;oBACf,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,IAAK;wBAC5C,kBAAkB,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG;wBACvC,MAAM,IAAI;4DAAQ,CAAA,UAAW,WAAW,SAAS;;oBACnD;oBAEA,kBAAkB;oBAClB,MAAM,IAAI;wDAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD,kBAAkB;oBAElB,2BAA2B;oBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAK;wBAC1C,MAAM,IAAI;4DAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD;4DAAU,CAAA,OAAQ;uCAAI;oCAAM;wCAC1B,MAAM;wCACN,MAAM,IAAI,MAAM,CAAC,EAAE;wCACnB,WAAW,KAAK,GAAG;oCACrB;iCAAE;;oBACJ;oBAEA,YAAY;oBACZ,MAAM,IAAI;wDAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD;wDAAuB,CAAA,OAAQ,OAAO;;gBACxC;;YAEA,MAAM,QAAQ,WAAW,SAAS;YAClC;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;KAAoB;IAExB,MAAM,qBAAqB,CAAC;QAC1B,uBAAuB;QACvB,UAAU,EAAE;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAKpD,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAI,WAAU;;4CAAgB;4CAAI;;;;;;;;;;;;;4BAIpC,OAAO,GAAG,CAAC,CAAC,MAAM,sBACjB,6LAAC;oCAAgB,WAAU;8CACxB,KAAK,IAAI,KAAK,0BACb,6LAAC;wCAAI,WAAU;kDAAsB,KAAK,IAAI;;;;;;mCAFxC;;;;;0CAQZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,6LAAC;kDAAM;;;;;;oCACN,4BAAc,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;4CAET,IAAI,IAAI;0DACT,6LAAC;0DAAM,IAAI,OAAO;;;;;;;uCALb;;;;;;;;;;;;;;;;;;;;;;0BAaf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GAtLM;KAAA;uCAwLS", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport AIParticleBackground from '../three/AIParticleBackground';\nimport AITerminal from '../ui/AITerminal';\nimport { ArrowDown, Sparkles, Shield, Clock } from 'lucide-react';\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const terminalRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial animations\n      gsap.set([titleRef.current, subtitleRef.current, terminalRef.current], {\n        opacity: 0,\n        y: 50\n      });\n\n      // Entrance animations\n      const tl = gsap.timeline();\n      \n      tl.to(titleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      })\n      .to(subtitleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.8,\n        ease: \"power3.out\"\n      }, \"-=0.5\")\n      .to(terminalRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      }, \"-=0.3\");\n\n      // Floating animations for decorative elements\n      floatingElementsRef.current.forEach((el, index) => {\n        if (el) {\n          gsap.to(el, {\n            y: -20,\n            duration: 2 + index * 0.5,\n            ease: \"power2.inOut\",\n            repeat: -1,\n            yoyo: true,\n            delay: index * 0.3\n          });\n        }\n      });\n\n      // Scroll-triggered morphing\n      ScrollTrigger.create({\n        trigger: heroRef.current,\n        start: \"top top\",\n        end: \"bottom top\",\n        scrub: 1,\n        onUpdate: (self) => {\n          const progress = self.progress;\n          gsap.to(titleRef.current, {\n            scale: 1 - progress * 0.1,\n            opacity: 1 - progress * 0.5,\n            duration: 0.3\n          });\n        }\n      });\n\n    }, heroRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToFloatingRefs = (el) => {\n    if (el && !floatingElementsRef.current.includes(el)) {\n      floatingElementsRef.current.push(el);\n    }\n  };\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden\"\n    >\n      {/* Three.js Background */}\n      <AIParticleBackground />\n\n      {/* Gradient Overlays */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-space/20 to-space/80\"></div>\n      <div className=\"absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent\"></div>\n\n      {/* Floating Decorative Elements */}\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-20 left-10 w-16 h-16 border border-primary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Shield className=\"w-8 h-8 text-primary\" />\n      </div>\n      \n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-32 right-16 w-12 h-12 border border-accent/30 rounded-lg flex items-center justify-center glass\"\n      >\n        <Clock className=\"w-6 h-6 text-accent\" />\n      </div>\n\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute bottom-32 left-20 w-20 h-20 border border-secondary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Sparkles className=\"w-10 h-10 text-secondary\" />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        \n        {/* Hero Title */}\n        <div ref={titleRef} className=\"mb-8\">\n          <h1 className=\"font-heading text-5xl md:text-7xl lg:text-8xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Never Miss\n            </span>\n            <br />\n            <span className=\"text-white text-glow-primary\">\n              A Warranty\n            </span>\n            <br />\n            <span className=\"bg-gradient-to-r from-accent via-primary to-secondary bg-clip-text text-transparent\">\n              Again\n            </span>\n          </h1>\n        </div>\n\n        {/* Hero Subtitle */}\n        <div ref={subtitleRef} className=\"mb-12\">\n          <p className=\"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\">\n            Smart AI assistant to track, manage, and remind you of all warranties, \n            services, and coverage across your entire digital and physical world.\n          </p>\n          <div className=\"flex flex-wrap justify-center gap-4 mt-6 text-sm\">\n            <span className=\"px-4 py-2 glass rounded-full text-primary border border-primary/30\">\n              🤖 AI-Powered Extraction\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-accent border border-accent/30\">\n              📱 Smart Reminders\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-secondary border border-secondary/30\">\n              🏠 3D Inventory\n            </span>\n          </div>\n        </div>\n\n        {/* AI Terminal Demo */}\n        <div ref={terminalRef} className=\"mb-12\">\n          <AITerminal />\n        </div>\n\n        {/* CTA Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n          <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105 glow-primary\">\n            Start Free Trial\n          </button>\n          <button className=\"border border-primary/50 px-8 py-4 rounded-full text-white font-semibold text-lg hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n            Watch Demo\n          </button>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ArrowDown className=\"w-6 h-6 text-white/60\" />\n        </div>\n      </div>\n\n      {/* Morphing Shapes */}\n      <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute bottom-1/4 right-1/4 w-48 h-48 bg-gradient-to-r from-accent/20 to-primary/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASA,wBAAwB;AACxB,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,cAAc;;IAClB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;6CAAC;oBACvB,qBAAqB;oBACrB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;wBAAC,SAAS,OAAO;wBAAE,YAAY,OAAO;wBAAE,YAAY,OAAO;qBAAC,EAAE;wBACrE,SAAS;wBACT,GAAG;oBACL;oBAEA,sBAAsB;oBACtB,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;oBAExB,GAAG,EAAE,CAAC,SAAS,OAAO,EAAE;wBACtB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GACC,EAAE,CAAC,YAAY,OAAO,EAAE;wBACvB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GAAG,SACF,EAAE,CAAC,YAAY,OAAO,EAAE;wBACvB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GAAG;oBAEH,8CAA8C;oBAC9C,oBAAoB,OAAO,CAAC,OAAO;qDAAC,CAAC,IAAI;4BACvC,IAAI,IAAI;gCACN,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,IAAI;oCACV,GAAG,CAAC;oCACJ,UAAU,IAAI,QAAQ;oCACtB,MAAM;oCACN,QAAQ,CAAC;oCACT,MAAM;oCACN,OAAO,QAAQ;gCACjB;4BACF;wBACF;;oBAEA,4BAA4B;oBAC5B,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS,QAAQ,OAAO;wBACxB,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ;yDAAE,CAAC;gCACT,MAAM,WAAW,KAAK,QAAQ;gCAC9B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;oCACxB,OAAO,IAAI,WAAW;oCACtB,SAAS,IAAI,WAAW;oCACxB,UAAU;gCACZ;4BACF;;oBACF;gBAEF;4CAAG;YAEH;yCAAO,IAAM,IAAI,MAAM;;QACzB;gCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,CAAC,oBAAoB,OAAO,CAAC,QAAQ,CAAC,KAAK;YACnD,oBAAoB,OAAO,CAAC,IAAI,CAAC;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC,qJAAA,CAAA,UAAoB;;;;;0BAGrB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,KAAK;wBAAU,WAAU;kCAC5B,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;8CAGtG,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAG/C,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;;;;;;;kCAO1G,6LAAC;wBAAI,KAAK;wBAAa,WAAU;;0CAC/B,6LAAC;gCAAE,WAAU;0CAAsE;;;;;;0CAInF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqE;;;;;;kDAGrF,6LAAC;wCAAK,WAAU;kDAAmE;;;;;;kDAGnF,6LAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;kCAO7F,6LAAC;wBAAI,KAAK;wBAAa,WAAU;kCAC/B,cAAA,6LAAC,wIAAA,CAAA,UAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAgN;;;;;;0CAGlO,6LAAC;gCAAO,WAAU;0CAA6J;;;;;;;;;;;;kCAMjL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAjLM;KAAA;uCAmLS", "debugId": null}}]}