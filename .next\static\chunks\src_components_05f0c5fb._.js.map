{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/three/AIParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useMemo } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Particle system component\nfunction ParticleField({ count = 1000 }) {\n  const mesh = useRef();\n  const light = useRef();\n\n  // Generate random positions for particles\n  const particles = useMemo(() => {\n    const temp = new Float32Array(count * 3);\n    for (let i = 0; i < count; i++) {\n      const i3 = i * 3;\n      temp[i3] = (Math.random() - 0.5) * 20;\n      temp[i3 + 1] = (Math.random() - 0.5) * 20;\n      temp[i3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return temp;\n  }, [count]);\n\n  // Animation loop\n  useFrame((state) => {\n    const time = state.clock.getElapsedTime();\n    \n    if (mesh.current) {\n      mesh.current.rotation.x = time * 0.1;\n      mesh.current.rotation.y = time * 0.05;\n      \n      // Update particle positions for floating effect\n      const positions = mesh.current.geometry.attributes.position.array;\n      for (let i = 0; i < positions.length; i += 3) {\n        positions[i + 1] += Math.sin(time + positions[i]) * 0.001;\n      }\n      mesh.current.geometry.attributes.position.needsUpdate = true;\n    }\n\n    if (light.current) {\n      light.current.position.x = Math.sin(time) * 5;\n      light.current.position.z = Math.cos(time) * 5;\n    }\n  });\n\n  return (\n    <group>\n      <Points ref={mesh} positions={particles} stride={3} frustumCulled={false}>\n        <PointMaterial\n          transparent\n          color=\"#00D4FF\"\n          size={0.05}\n          sizeAttenuation={true}\n          depthWrite={false}\n          blending={THREE.AdditiveBlending}\n        />\n      </Points>\n      \n      {/* AI Network Lines */}\n      <mesh>\n        <sphereGeometry args={[8, 32, 32]} />\n        <meshBasicMaterial\n          color=\"#8B5CF6\"\n          transparent\n          opacity={0.1}\n          wireframe\n        />\n      </mesh>\n\n      {/* Floating light */}\n      <pointLight\n        ref={light}\n        color=\"#00FF88\"\n        intensity={0.5}\n        distance={10}\n        decay={2}\n      />\n    </group>\n  );\n}\n\n// Neural network connections\nfunction NeuralNetwork() {\n  const lines = useRef();\n\n  useFrame((state) => {\n    if (lines.current) {\n      lines.current.rotation.y = state.clock.getElapsedTime() * 0.1;\n    }\n  });\n\n  const connections = useMemo(() => {\n    const points = [];\n    const nodeCount = 20;\n    \n    // Create nodes in 3D space\n    const nodes = [];\n    for (let i = 0; i < nodeCount; i++) {\n      nodes.push(new THREE.Vector3(\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15\n      ));\n    }\n\n    // Connect nearby nodes\n    for (let i = 0; i < nodes.length; i++) {\n      for (let j = i + 1; j < nodes.length; j++) {\n        if (nodes[i].distanceTo(nodes[j]) < 5) {\n          points.push(nodes[i], nodes[j]);\n        }\n      }\n    }\n\n    return points;\n  }, []);\n\n  return (\n    <group ref={lines}>\n      <lineSegments>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={connections.length}\n            array={new Float32Array(connections.flatMap(v => [v.x, v.y, v.z]))}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <lineBasicMaterial\n          color=\"#00D4FF\"\n          transparent\n          opacity={0.3}\n          blending={THREE.AdditiveBlending}\n        />\n      </lineSegments>\n    </group>\n  );\n}\n\n// Main background component\nconst AIParticleBackground = () => {\n  return (\n    <div className=\"absolute inset-0 w-full h-full\">\n      <Canvas\n        camera={{ position: [0, 0, 10], fov: 60 }}\n        style={{ background: 'transparent' }}\n      >\n        <ambientLight intensity={0.2} />\n        <ParticleField count={1500} />\n        <NeuralNetwork />\n        \n        {/* Fog for depth */}\n        <fog attach=\"fog\" args={['#0A0A0F', 10, 50]} />\n      </Canvas>\n    </div>\n  );\n};\n\nexport default AIParticleBackground;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,4BAA4B;AAC5B,SAAS,cAAc,EAAE,QAAQ,IAAI,EAAE;;IACrC,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAClB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEnB,0CAA0C;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACxB,MAAM,OAAO,IAAI,aAAa,QAAQ;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,KAAK,IAAI;gBACf,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACvC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC;YACA,OAAO;QACT;2CAAG;QAAC;KAAM;IAEV,iBAAiB;IACjB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,MAAM,OAAO,MAAM,KAAK,CAAC,cAAc;YAEvC,IAAI,KAAK,OAAO,EAAE;gBAChB,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;gBACjC,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;gBAEjC,gDAAgD;gBAChD,MAAM,YAAY,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK;gBACjE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;oBAC5C,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,SAAS,CAAC,EAAE,IAAI;gBACtD;gBACA,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;YAC1D;YAEA,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;gBAC5C,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;YAC9C;QACF;;IAEA,qBACE,6LAAC;;0BACC,6LAAC,6JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAM,WAAW;gBAAW,QAAQ;gBAAG,eAAe;0BACjE,cAAA,6LAAC,oKAAA,CAAA,gBAAa;oBACZ,WAAW;oBACX,OAAM;oBACN,MAAM;oBACN,iBAAiB;oBACjB,YAAY;oBACZ,UAAU,kJAAA,CAAA,mBAAsB;;;;;;;;;;;0BAKpC,6LAAC;;kCACC,6LAAC;wBAAe,MAAM;4BAAC;4BAAG;4BAAI;yBAAG;;;;;;kCACjC,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,SAAS;;;;;;;;;;;;0BAKb,6LAAC;gBACC,KAAK;gBACL,OAAM;gBACN,WAAW;gBACX,UAAU;gBACV,OAAO;;;;;;;;;;;;AAIf;GAxES;;QAiBP,kNAAA,CAAA,WAAQ;;;KAjBD;AA0ET,6BAA6B;AAC7B,SAAS;;IACP,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEnB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,cAAc,KAAK;YAC5D;QACF;;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC1B,MAAM,SAAS,EAAE;YACjB,MAAM,YAAY;YAElB,2BAA2B;YAC3B,MAAM,QAAQ,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,MAAM,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAC1B,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE5B;YAEA,uBAAuB;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACzC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG;wBACrC,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;oBAChC;gBACF;YACF;YAEA,OAAO;QACT;6CAAG,EAAE;IAEL,qBACE,6LAAC;QAAM,KAAK;kBACV,cAAA,6LAAC;;8BACC,6LAAC;8BACC,cAAA,6LAAC;wBACC,QAAO;wBACP,OAAO,YAAY,MAAM;wBACzB,OAAO,IAAI,aAAa,YAAY,OAAO,CAAC,CAAA,IAAK;gCAAC,EAAE,CAAC;gCAAE,EAAE,CAAC;gCAAE,EAAE,CAAC;6BAAC;wBAChE,UAAU;;;;;;;;;;;8BAGd,6LAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,UAAU,kJAAA,CAAA,mBAAsB;;;;;;;;;;;;;;;;;AAK1C;IAvDS;;QAGP,kNAAA,CAAA,WAAQ;;;MAHD;AAyDT,4BAA4B;AAC5B,MAAM,uBAAuB;IAC3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAG;gBAAE,KAAK;YAAG;YACxC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,6LAAC;oBAAa,WAAW;;;;;;8BACzB,6LAAC;oBAAc,OAAO;;;;;;8BACtB,6LAAC;;;;;8BAGD,6LAAC;oBAAI,QAAO;oBAAM,MAAM;wBAAC;wBAAW;wBAAI;qBAAG;;;;;;;;;;;;;;;;;AAInD;MAhBM;uCAkBS", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/AITerminal.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Terminal, Play, Zap, FileText, Camera, Bell } from 'lucide-react';\n\nconst AITerminal = () => {\n  const [currentCommand, setCurrentCommand] = useState('');\n  const [output, setOutput] = useState([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showCursor, setShowCursor] = useState(true);\n  const terminalRef = useRef(null);\n\n  const commands = [\n    {\n      command: 'scan receipt.jpg',\n      output: [\n        'Analyzing receipt...',\n        'Product: iPhone 15 Pro',\n        'Brand: Apple',\n        'Purchase Date: 2024-01-15',\n        'Warranty: 1 year (expires 2025-01-15)',\n        '✓ Added to warranty tracker'\n      ],\n      icon: <Camera className=\"w-4 h-4\" />\n    },\n    {\n      command: 'check warranties',\n      output: [\n        'Scanning warranty database...',\n        '📱 iPhone 15 Pro - 11 months remaining',\n        '💻 MacBook Pro - 8 months remaining',\n        '🏠 Samsung Fridge - 2 years remaining',\n        '⚠️  3 items expiring in 30 days'\n      ],\n      icon: <FileText className=\"w-4 h-4\" />\n    },\n    {\n      command: 'set reminder AC filter',\n      output: [\n        'Creating smart reminder...',\n        'Reminder: Replace AC filter',\n        'Frequency: Every 3 months',\n        'Next due: March 15, 2024',\n        '✓ Reminder activated'\n      ],\n      icon: <Bell className=\"w-4 h-4\" />\n    }\n  ];\n\n  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);\n\n  // Cursor blinking effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Auto-demo cycle\n  useEffect(() => {\n    const runDemo = async () => {\n      if (currentCommandIndex >= commands.length) {\n        // Reset after all commands\n        setTimeout(() => {\n          setOutput([]);\n          setCurrentCommandIndex(0);\n        }, 3000);\n        return;\n      }\n\n      const cmd = commands[currentCommandIndex];\n      setIsTyping(true);\n      \n      // Type command\n      for (let i = 0; i <= cmd.command.length; i++) {\n        setCurrentCommand(cmd.command.slice(0, i));\n        await new Promise(resolve => setTimeout(resolve, 50));\n      }\n\n      // Execute command\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setCurrentCommand('');\n      \n      // Show output line by line\n      for (let i = 0; i < cmd.output.length; i++) {\n        await new Promise(resolve => setTimeout(resolve, 300));\n        setOutput(prev => [...prev, {\n          type: 'output',\n          text: cmd.output[i],\n          timestamp: Date.now()\n        }]);\n      }\n\n      setIsTyping(false);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setCurrentCommandIndex(prev => prev + 1);\n    };\n\n    const timer = setTimeout(runDemo, 1000);\n    return () => clearTimeout(timer);\n  }, [currentCommandIndex]);\n\n  const handleCommandClick = (cmdIndex) => {\n    setCurrentCommandIndex(cmdIndex);\n    setOutput([]);\n  };\n\n  return (\n    <div className=\"relative w-full max-w-4xl mx-auto\">\n      {/* Terminal Window */}\n      <div className=\"glass rounded-lg overflow-hidden border border-primary/20 shadow-2xl\">\n        {/* Terminal Header */}\n        <div className=\"flex items-center justify-between px-4 py-3 bg-surface/50 border-b border-primary/20\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex space-x-2\">\n              <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n            </div>\n            <Terminal className=\"w-4 h-4 text-primary ml-4\" />\n            <span className=\"text-sm font-mono text-white/80\">WarrantyAI Terminal v2.0</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 rounded-full bg-accent animate-pulse\"></div>\n            <span className=\"text-xs text-accent font-mono\">ONLINE</span>\n          </div>\n        </div>\n\n        {/* Terminal Content */}\n        <div \n          ref={terminalRef}\n          className=\"p-6 h-80 overflow-y-auto font-mono text-sm bg-gradient-to-b from-space/80 to-surface/80\"\n        >\n          {/* Welcome Message */}\n          <div className=\"text-accent mb-4\">\n            <div>Welcome to WarrantyAI Terminal</div>\n            <div className=\"text-white/60\">Type commands or click suggestions below</div>\n            <div className=\"text-primary\">{'>'} Ready for input...</div>\n          </div>\n\n          {/* Output History */}\n          {output.map((line, index) => (\n            <div key={index} className=\"mb-1\">\n              {line.type === 'output' && (\n                <div className=\"text-white/80 pl-4\">{line.text}</div>\n              )}\n            </div>\n          ))}\n\n          {/* Current Command Line */}\n          <div className=\"flex items-center text-primary\">\n            <span className=\"mr-2\">{'>'}</span>\n            <span>{currentCommand}</span>\n            {showCursor && <span className=\"bg-primary w-2 h-4 ml-1 animate-pulse\">|</span>}\n          </div>\n        </div>\n\n        {/* Command Suggestions */}\n        <div className=\"p-4 bg-surface/30 border-t border-primary/20\">\n          <div className=\"text-xs text-white/60 mb-2\">Quick Commands:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {commands.map((cmd, index) => (\n              <button\n                key={index}\n                onClick={() => handleCommandClick(index)}\n                className=\"flex items-center space-x-2 px-3 py-1 bg-primary/10 hover:bg-primary/20 rounded border border-primary/30 transition-all duration-300 text-xs text-white/80 hover:text-white\"\n              >\n                {cmd.icon}\n                <span>{cmd.command}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Buttons */}\n      <div className=\"absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-4\">\n        <button className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Play className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n        <button className=\"w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center shadow-lg hover:shadow-accent/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Zap className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AITerminal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,WAAW;QACf;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW;iDAAY;oBAC3B;yDAAc,CAAA,OAAQ,CAAC;;gBACzB;gDAAG;YACH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;gDAAU;oBACd,IAAI,uBAAuB,SAAS,MAAM,EAAE;wBAC1C,2BAA2B;wBAC3B;4DAAW;gCACT,UAAU,EAAE;gCACZ,uBAAuB;4BACzB;2DAAG;wBACH;oBACF;oBAEA,MAAM,MAAM,QAAQ,CAAC,oBAAoB;oBACzC,YAAY;oBAEZ,eAAe;oBACf,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,IAAK;wBAC5C,kBAAkB,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG;wBACvC,MAAM,IAAI;4DAAQ,CAAA,UAAW,WAAW,SAAS;;oBACnD;oBAEA,kBAAkB;oBAClB,MAAM,IAAI;wDAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD,kBAAkB;oBAElB,2BAA2B;oBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAK;wBAC1C,MAAM,IAAI;4DAAQ,CAAA,UAAW,WAAW,SAAS;;wBACjD;4DAAU,CAAA,OAAQ;uCAAI;oCAAM;wCAC1B,MAAM;wCACN,MAAM,IAAI,MAAM,CAAC,EAAE;wCACnB,WAAW,KAAK,GAAG;oCACrB;iCAAE;;oBACJ;oBAEA,YAAY;oBACZ,MAAM,IAAI;wDAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD;wDAAuB,CAAA,OAAQ,OAAO;;gBACxC;;YAEA,MAAM,QAAQ,WAAW,SAAS;YAClC;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;KAAoB;IAExB,MAAM,qBAAqB,CAAC;QAC1B,uBAAuB;QACvB,UAAU,EAAE;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAKpD,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAI,WAAU;;4CAAgB;4CAAI;;;;;;;;;;;;;4BAIpC,OAAO,GAAG,CAAC,CAAC,MAAM,sBACjB,6LAAC;oCAAgB,WAAU;8CACxB,KAAK,IAAI,KAAK,0BACb,6LAAC;wCAAI,WAAU;kDAAsB,KAAK,IAAI;;;;;;mCAFxC;;;;;0CAQZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,6LAAC;kDAAM;;;;;;oCACN,4BAAc,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;4CAET,IAAI,IAAI;0DACT,6LAAC;0DAAM,IAAI,OAAO;;;;;;;uCALb;;;;;;;;;;;;;;;;;;;;;;0BAaf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GAtLM;KAAA;uCAwLS", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport AIParticleBackground from '../three/AIParticleBackground';\nimport AITerminal from '../ui/AITerminal';\nimport { ArrowDown, Sparkles, Shield, Clock } from 'lucide-react';\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const terminalRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial animations\n      gsap.set([titleRef.current, subtitleRef.current, terminalRef.current], {\n        opacity: 0,\n        y: 50\n      });\n\n      // Entrance animations\n      const tl = gsap.timeline();\n      \n      tl.to(titleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      })\n      .to(subtitleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.8,\n        ease: \"power3.out\"\n      }, \"-=0.5\")\n      .to(terminalRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      }, \"-=0.3\");\n\n      // Floating animations for decorative elements\n      floatingElementsRef.current.forEach((el, index) => {\n        if (el) {\n          gsap.to(el, {\n            y: -20,\n            duration: 2 + index * 0.5,\n            ease: \"power2.inOut\",\n            repeat: -1,\n            yoyo: true,\n            delay: index * 0.3\n          });\n        }\n      });\n\n      // Scroll-triggered morphing\n      ScrollTrigger.create({\n        trigger: heroRef.current,\n        start: \"top top\",\n        end: \"bottom top\",\n        scrub: 1,\n        onUpdate: (self) => {\n          const progress = self.progress;\n          gsap.to(titleRef.current, {\n            scale: 1 - progress * 0.1,\n            opacity: 1 - progress * 0.5,\n            duration: 0.3\n          });\n        }\n      });\n\n    }, heroRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToFloatingRefs = (el) => {\n    if (el && !floatingElementsRef.current.includes(el)) {\n      floatingElementsRef.current.push(el);\n    }\n  };\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden\"\n    >\n      {/* Three.js Background */}\n      <AIParticleBackground />\n\n      {/* Gradient Overlays */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-space/20 to-space/80\"></div>\n      <div className=\"absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent\"></div>\n\n      {/* Floating Decorative Elements */}\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-20 left-10 w-16 h-16 border border-primary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Shield className=\"w-8 h-8 text-primary\" />\n      </div>\n      \n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-32 right-16 w-12 h-12 border border-accent/30 rounded-lg flex items-center justify-center glass\"\n      >\n        <Clock className=\"w-6 h-6 text-accent\" />\n      </div>\n\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute bottom-32 left-20 w-20 h-20 border border-secondary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Sparkles className=\"w-10 h-10 text-secondary\" />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        \n        {/* Hero Title */}\n        <div ref={titleRef} className=\"mb-8\">\n          <h1 className=\"font-heading text-5xl md:text-7xl lg:text-8xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Never Miss\n            </span>\n            <br />\n            <span className=\"text-white text-glow-primary\">\n              A Warranty\n            </span>\n            <br />\n            <span className=\"bg-gradient-to-r from-accent via-primary to-secondary bg-clip-text text-transparent\">\n              Again\n            </span>\n          </h1>\n        </div>\n\n        {/* Hero Subtitle */}\n        <div ref={subtitleRef} className=\"mb-12\">\n          <p className=\"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\">\n            Smart AI assistant to track, manage, and remind you of all warranties, \n            services, and coverage across your entire digital and physical world.\n          </p>\n          <div className=\"flex flex-wrap justify-center gap-4 mt-6 text-sm\">\n            <span className=\"px-4 py-2 glass rounded-full text-primary border border-primary/30\">\n              🤖 AI-Powered Extraction\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-accent border border-accent/30\">\n              📱 Smart Reminders\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-secondary border border-secondary/30\">\n              🏠 3D Inventory\n            </span>\n          </div>\n        </div>\n\n        {/* AI Terminal Demo */}\n        <div ref={terminalRef} className=\"mb-12\">\n          <AITerminal />\n        </div>\n\n        {/* CTA Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n          <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105 glow-primary\">\n            Start Free Trial\n          </button>\n          <button className=\"border border-primary/50 px-8 py-4 rounded-full text-white font-semibold text-lg hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n            Watch Demo\n          </button>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ArrowDown className=\"w-6 h-6 text-white/60\" />\n        </div>\n      </div>\n\n      {/* Morphing Shapes */}\n      <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute bottom-1/4 right-1/4 w-48 h-48 bg-gradient-to-r from-accent/20 to-primary/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AASA,wBAAwB;AACxB,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,cAAc;;IAClB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;6CAAC;oBACvB,qBAAqB;oBACrB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;wBAAC,SAAS,OAAO;wBAAE,YAAY,OAAO;wBAAE,YAAY,OAAO;qBAAC,EAAE;wBACrE,SAAS;wBACT,GAAG;oBACL;oBAEA,sBAAsB;oBACtB,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ;oBAExB,GAAG,EAAE,CAAC,SAAS,OAAO,EAAE;wBACtB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GACC,EAAE,CAAC,YAAY,OAAO,EAAE;wBACvB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GAAG,SACF,EAAE,CAAC,YAAY,OAAO,EAAE;wBACvB,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;oBACR,GAAG;oBAEH,8CAA8C;oBAC9C,oBAAoB,OAAO,CAAC,OAAO;qDAAC,CAAC,IAAI;4BACvC,IAAI,IAAI;gCACN,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,IAAI;oCACV,GAAG,CAAC;oCACJ,UAAU,IAAI,QAAQ;oCACtB,MAAM;oCACN,QAAQ,CAAC;oCACT,MAAM;oCACN,OAAO,QAAQ;gCACjB;4BACF;wBACF;;oBAEA,4BAA4B;oBAC5B,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS,QAAQ,OAAO;wBACxB,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ;yDAAE,CAAC;gCACT,MAAM,WAAW,KAAK,QAAQ;gCAC9B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;oCACxB,OAAO,IAAI,WAAW;oCACtB,SAAS,IAAI,WAAW;oCACxB,UAAU;gCACZ;4BACF;;oBACF;gBAEF;4CAAG;YAEH;yCAAO,IAAM,IAAI,MAAM;;QACzB;gCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,CAAC,oBAAoB,OAAO,CAAC,QAAQ,CAAC,KAAK;YACnD,oBAAoB,OAAO,CAAC,IAAI,CAAC;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC,qJAAA,CAAA,UAAoB;;;;;0BAGrB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,KAAK;wBAAU,WAAU;kCAC5B,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;8CAGtG,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAG/C,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;;;;;;;kCAO1G,6LAAC;wBAAI,KAAK;wBAAa,WAAU;;0CAC/B,6LAAC;gCAAE,WAAU;0CAAsE;;;;;;0CAInF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqE;;;;;;kDAGrF,6LAAC;wCAAK,WAAU;kDAAmE;;;;;;kDAGnF,6LAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;kCAO7F,6LAAC;wBAAI,KAAK;wBAAa,WAAU;kCAC/B,cAAA,6LAAC,wIAAA,CAAA,UAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAgN;;;;;;0CAGlO,6LAAC;gCAAO,WAAU;0CAA6J;;;;;;;;;;;;kCAMjL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAjLM;KAAA;uCAmLS", "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { AlertTriangle, CheckCircle, Receipt, Clock, Shield, Smartphone } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ProblemSolutionSection = () => {\n  const sectionRef = useRef(null);\n  const problemRef = useRef(null);\n  const solutionRef = useRef(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Split screen animation\n      gsap.set([problemRef.current, solutionRef.current], {\n        x: (index) => index === 0 ? -100 : 100,\n        opacity: 0\n      });\n\n      ScrollTrigger.create({\n        trigger: sectionRef.current,\n        start: \"top 80%\",\n        end: \"bottom 20%\",\n        onEnter: () => {\n          gsap.to([problemRef.current, solutionRef.current], {\n            x: 0,\n            opacity: 1,\n            duration: 1,\n            stagger: 0.2,\n            ease: \"power3.out\"\n          });\n        }\n      });\n\n      // Floating animation for icons\n      gsap.to(\".floating-icon\", {\n        y: -10,\n        duration: 2,\n        ease: \"power2.inOut\",\n        repeat: -1,\n        yoyo: true,\n        stagger: 0.3\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const problems = [\n    {\n      icon: <Receipt className=\"w-8 h-8\" />,\n      title: \"Lost Receipts\",\n      description: \"70% of people lose or misplace important receipts and warranty documents\"\n    },\n    {\n      icon: <Clock className=\"w-8 h-8\" />,\n      title: \"Missed Deadlines\",\n      description: \"Billions in unclaimed warranties due to forgotten expiration dates\"\n    },\n    {\n      icon: <AlertTriangle className=\"w-8 h-8\" />,\n      title: \"Manual Tracking\",\n      description: \"Chaotic spreadsheets and sticky notes for managing multiple warranties\"\n    }\n  ];\n\n  const solutions = [\n    {\n      icon: <Smartphone className=\"w-8 h-8\" />,\n      title: \"AI-Powered Scanning\",\n      description: \"Instantly extract warranty info from receipts, emails, and photos\"\n    },\n    {\n      icon: <Shield className=\"w-8 h-8\" />,\n      title: \"Smart Reminders\",\n      description: \"Never miss a warranty claim or service deadline again\"\n    },\n    {\n      icon: <CheckCircle className=\"w-8 h-8\" />,\n      title: \"Centralized Management\",\n      description: \"All your warranties, services, and coverage in one intelligent dashboard\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">The Problem</span>\n            <span className=\"text-primary mx-4\">→</span>\n            <span className=\"bg-gradient-to-r from-accent to-primary bg-clip-text text-transparent\">\n              Our Solution\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n            Warranty management is broken. We're fixing it with AI.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Problem Side */}\n          <div ref={problemRef} className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"font-heading text-3xl font-bold text-white mb-4\">\n                Current Pain Points\n              </h3>\n              <p className=\"text-white/70 text-lg\">\n                Managing warranties and service schedules is a nightmare for consumers and businesses alike.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {problems.map((problem, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-start space-x-4 p-6 glass rounded-lg border border-red-500/20 hover:border-red-500/40 transition-all duration-300\"\n                >\n                  <div className=\"floating-icon text-red-400 mt-1\">\n                    {problem.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-white text-lg mb-2\">\n                      {problem.title}\n                    </h4>\n                    <p className=\"text-white/70\">\n                      {problem.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Problem Stats */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              <div className=\"text-center p-4 glass rounded-lg border border-red-500/20\">\n                <div className=\"text-2xl font-bold text-red-400\">$2.1B</div>\n                <div className=\"text-sm text-white/60\">Unclaimed Warranties</div>\n              </div>\n              <div className=\"text-center p-4 glass rounded-lg border border-red-500/20\">\n                <div className=\"text-2xl font-bold text-red-400\">70%</div>\n                <div className=\"text-sm text-white/60\">Lost Receipts</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Solution Side */}\n          <div ref={solutionRef} className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"font-heading text-3xl font-bold text-white mb-4\">\n                WarrantyAI Solution\n              </h3>\n              <p className=\"text-white/70 text-lg\">\n                Intelligent automation that transforms how you manage warranties and asset coverage.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {solutions.map((solution, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-start space-x-4 p-6 glass rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300 hover:glow-accent\"\n                >\n                  <div className=\"floating-icon text-accent mt-1\">\n                    {solution.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-white text-lg mb-2\">\n                      {solution.title}\n                    </h4>\n                    <p className=\"text-white/70\">\n                      {solution.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Solution Benefits */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              <div className=\"text-center p-4 glass rounded-lg border border-accent/20\">\n                <div className=\"text-2xl font-bold text-accent\">100%</div>\n                <div className=\"text-sm text-white/60\">Warranty Tracking</div>\n              </div>\n              <div className=\"text-center p-4 glass rounded-lg border border-accent/20\">\n                <div className=\"text-2xl font-bold text-accent\">0</div>\n                <div className=\"text-sm text-white/60\">Missed Claims</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Connecting Arrow */}\n        <div className=\"hidden lg:flex absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse-glow\">\n            <span className=\"text-2xl\">→</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute top-10 left-10 w-32 h-32 bg-gradient-to-r from-red-500/10 to-transparent rounded-full blur-xl\"></div>\n      <div className=\"absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-r from-accent/10 to-transparent rounded-full blur-xl\"></div>\n    </section>\n  );\n};\n\nexport default ProblemSolutionSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,yBAAyB;;IAC7B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;wDAAC;oBACvB,yBAAyB;oBACzB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;wBAAC,WAAW,OAAO;wBAAE,YAAY,OAAO;qBAAC,EAAE;wBAClD,CAAC;oEAAE,CAAC,QAAU,UAAU,IAAI,CAAC,MAAM;;wBACnC,SAAS;oBACX;oBAEA,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS,WAAW,OAAO;wBAC3B,OAAO;wBACP,KAAK;wBACL,OAAO;oEAAE;gCACP,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC;oCAAC,WAAW,OAAO;oCAAE,YAAY,OAAO;iCAAC,EAAE;oCACjD,GAAG;oCACH,SAAS;oCACT,UAAU;oCACV,SAAS;oCACT,MAAM;gCACR;4BACF;;oBACF;oBAEA,+BAA+B;oBAC/B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,kBAAkB;wBACxB,GAAG,CAAC;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ,CAAC;wBACT,MAAM;wBACN,SAAS;oBACX;gBAEF;uDAAG;YAEH;oDAAO,IAAM,IAAI,MAAM;;QACzB;2CAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YACE,oBAAM,6LAAC,iNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,6LAAC;wCAAK,WAAU;kDAAwE;;;;;;;;;;;;0CAI1F,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,KAAK;gCAAY,WAAU;;kDAC9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI;;;;;;kEAEf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;+CAXnB;;;;;;;;;;kDAmBX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,KAAK;gCAAa,WAAU;;kDAC/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI;;;;;;kEAEhB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,SAAS,KAAK;;;;;;0EAEjB,6LAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;;;;;;;;+CAXpB;;;;;;;;;;kDAmBX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAzMM;KAAA;uCA2MS", "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/ThreeStepSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { Upload, Brain, Bell, ArrowRight } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ThreeStepSection = () => {\n  const sectionRef = useRef(null);\n  const timelineRef = useRef(null);\n  const stepsRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial state\n      gsap.set(stepsRef.current, {\n        opacity: 0,\n        y: 50,\n        scale: 0.8\n      });\n\n      gsap.set(\".step-connector\", {\n        scaleX: 0,\n        transformOrigin: \"left center\"\n      });\n\n      // Timeline animation\n      const tl = gsap.timeline({\n        scrollTrigger: {\n          trigger: sectionRef.current,\n          start: \"top 70%\",\n          end: \"bottom 30%\",\n          toggleActions: \"play none none reverse\"\n        }\n      });\n\n      // Animate steps in sequence\n      stepsRef.current.forEach((step, index) => {\n        tl.to(step, {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.6,\n          ease: \"back.out(1.7)\"\n        }, index * 0.3);\n\n        // Animate connector after step\n        if (index < stepsRef.current.length - 1) {\n          tl.to(`.step-connector-${index}`, {\n            scaleX: 1,\n            duration: 0.4,\n            ease: \"power2.out\"\n          }, index * 0.3 + 0.3);\n        }\n      });\n\n      // Floating animation for icons\n      gsap.to(\".step-icon\", {\n        y: -5,\n        duration: 2,\n        ease: \"power2.inOut\",\n        repeat: -1,\n        yoyo: true,\n        stagger: 0.5\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToStepsRefs = (el) => {\n    if (el && !stepsRef.current.includes(el)) {\n      stepsRef.current.push(el);\n    }\n  };\n\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Upload & Scan\",\n      description: \"Simply upload receipts, photos, or forward emails. Our AI instantly extracts product details, warranty info, and service dates.\",\n      icon: <Upload className=\"w-8 h-8\" />,\n      color: \"primary\",\n      features: [\"Receipt scanning\", \"Email integration\", \"Photo recognition\", \"Bulk upload\"]\n    },\n    {\n      number: \"02\", \n      title: \"AI Processing\",\n      description: \"Advanced machine learning analyzes your items, identifies warranty terms, and creates smart reminders for optimal timing.\",\n      icon: <Brain className=\"w-8 h-8\" />,\n      color: \"secondary\",\n      features: [\"Smart extraction\", \"Warranty detection\", \"Service scheduling\", \"Data validation\"]\n    },\n    {\n      number: \"03\",\n      title: \"Smart Alerts\",\n      description: \"Receive timely notifications before warranties expire, service is due, or claims need to be filed. Never miss another deadline.\",\n      icon: <Bell className=\"w-8 h-8\" />,\n      color: \"accent\",\n      features: [\"Proactive reminders\", \"Custom alerts\", \"Claim assistance\", \"Calendar sync\"]\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">How It Works</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              3 Simple Steps\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n            From chaos to clarity in minutes. Our AI handles the complexity so you don't have to.\n          </p>\n        </div>\n\n        {/* Timeline */}\n        <div ref={timelineRef} className=\"relative\">\n          {/* Desktop Timeline */}\n          <div className=\"hidden lg:flex items-center justify-between mb-16\">\n            {steps.map((step, index) => (\n              <div key={index} className=\"flex items-center\">\n                {/* Step */}\n                <div \n                  ref={addToStepsRefs}\n                  className={`relative z-10 w-32 h-32 rounded-full border-4 border-${step.color} bg-gradient-to-br from-${step.color}/20 to-transparent flex items-center justify-center group hover:scale-110 transition-all duration-300`}\n                >\n                  <div className=\"step-icon text-white group-hover:text-accent transition-colors duration-300\">\n                    {step.icon}\n                  </div>\n                  <div className={`absolute -top-2 -right-2 w-8 h-8 bg-${step.color} rounded-full flex items-center justify-center text-xs font-bold text-space`}>\n                    {step.number}\n                  </div>\n                </div>\n\n                {/* Connector */}\n                {index < steps.length - 1 && (\n                  <div className={`step-connector step-connector-${index} flex-1 h-1 bg-gradient-to-r from-${step.color} to-${steps[index + 1].color} mx-8`}></div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Step Details */}\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {steps.map((step, index) => (\n              <div \n                key={index}\n                ref={addToStepsRefs}\n                className=\"group\"\n              >\n                {/* Mobile Step Icon */}\n                <div className=\"lg:hidden mb-6\">\n                  <div className={`w-20 h-20 rounded-full border-4 border-${step.color} bg-gradient-to-br from-${step.color}/20 to-transparent flex items-center justify-center mx-auto`}>\n                    <div className=\"step-icon text-white\">\n                      {step.icon}\n                    </div>\n                    <div className={`absolute -top-2 -right-2 w-6 h-6 bg-${step.color} rounded-full flex items-center justify-center text-xs font-bold text-space`}>\n                      {step.number}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Content Card */}\n                <div className=\"glass rounded-lg p-6 h-full border border-white/10 hover:border-white/20 transition-all duration-300 group-hover:glow-primary\">\n                  <div className=\"text-center lg:text-left\">\n                    <h3 className=\"font-heading text-2xl font-bold text-white mb-4\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-white/80 mb-6 leading-relaxed\">\n                      {step.description}\n                    </p>\n\n                    {/* Features */}\n                    <div className=\"space-y-2\">\n                      {step.features.map((feature, featureIndex) => (\n                        <div key={featureIndex} className=\"flex items-center space-x-2\">\n                          <div className={`w-2 h-2 rounded-full bg-${step.color}`}></div>\n                          <span className=\"text-sm text-white/70\">{feature}</span>\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* Action Button */}\n                    <button className={`mt-6 w-full bg-gradient-to-r from-${step.color}/20 to-${step.color}/10 border border-${step.color}/30 px-4 py-2 rounded-lg text-white hover:bg-${step.color}/30 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105`}>\n                      <span>Learn More</span>\n                      <ArrowRight className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105 glow-primary\">\n            Try It Now - Free\n          </button>\n          <p className=\"text-white/60 mt-4\">No credit card required • 30-day free trial</p>\n        </div>\n      </div>\n\n      {/* Background Decorations */}\n      <div className=\"absolute top-20 left-10 w-24 h-24 bg-gradient-to-r from-primary/20 to-transparent rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-secondary/20 to-transparent rounded-full blur-xl animate-pulse delay-1000\"></div>\n      <div className=\"absolute bottom-20 left-1/3 w-28 h-28 bg-gradient-to-r from-accent/20 to-transparent rounded-full blur-xl animate-pulse delay-2000\"></div>\n    </section>\n  );\n};\n\nexport default ThreeStepSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,mBAAmB;;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;kDAAC;oBACvB,gBAAgB;oBAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;wBACzB,SAAS;wBACT,GAAG;wBACH,OAAO;oBACT;oBAEA,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,mBAAmB;wBAC1B,QAAQ;wBACR,iBAAiB;oBACnB;oBAEA,qBAAqB;oBACrB,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;wBACvB,eAAe;4BACb,SAAS,WAAW,OAAO;4BAC3B,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;oBAEA,4BAA4B;oBAC5B,SAAS,OAAO,CAAC,OAAO;0DAAC,CAAC,MAAM;4BAC9B,GAAG,EAAE,CAAC,MAAM;gCACV,SAAS;gCACT,GAAG;gCACH,OAAO;gCACP,UAAU;gCACV,MAAM;4BACR,GAAG,QAAQ;4BAEX,+BAA+B;4BAC/B,IAAI,QAAQ,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;gCACvC,GAAG,EAAE,CAAC,CAAC,gBAAgB,EAAE,OAAO,EAAE;oCAChC,QAAQ;oCACR,UAAU;oCACV,MAAM;gCACR,GAAG,QAAQ,MAAM;4BACnB;wBACF;;oBAEA,+BAA+B;oBAC/B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,cAAc;wBACpB,GAAG,CAAC;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ,CAAC;wBACT,MAAM;wBACN,SAAS;oBACX;gBAEF;iDAAG;YAEH;8CAAO,IAAM,IAAI,MAAM;;QACzB;qCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK;YACxC,SAAS,OAAO,CAAC,IAAI,CAAC;QACxB;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,UAAU;gBAAC;gBAAoB;gBAAqB;gBAAqB;aAAc;QACzF;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;gBAAC;gBAAoB;gBAAsB;gBAAsB;aAAkB;QAC/F;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,UAAU;gBAAC;gBAAuB;gBAAiB;gBAAoB;aAAgB;QACzF;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,KAAK;wBAAa,WAAU;;0CAE/B,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAAgB,WAAU;;0DAEzB,6LAAC;gDACC,KAAK;gDACL,WAAW,CAAC,qDAAqD,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,qGAAqG,CAAC;;kEAEzN,6LAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAI,WAAW,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,2EAA2E,CAAC;kEAC3I,KAAK,MAAM;;;;;;;;;;;;4CAKf,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;gDAAI,WAAW,CAAC,8BAA8B,EAAE,MAAM,kCAAkC,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;;;uCAhBnI;;;;;;;;;;0CAuBd,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAEC,KAAK;wCACL,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAW,CAAC,uCAAuC,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,2DAA2D,CAAC;;sEACpK,6LAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAI,WAAW,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,2EAA2E,CAAC;sEAC3I,KAAK,MAAM;;;;;;;;;;;;;;;;;0DAMlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,6LAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;oEAAuB,WAAU;;sFAChC,6LAAC;4EAAI,WAAW,CAAC,wBAAwB,EAAE,KAAK,KAAK,EAAE;;;;;;sFACvD,6LAAC;4EAAK,WAAU;sFAAyB;;;;;;;mEAFjC;;;;;;;;;;sEAQd,6LAAC;4DAAO,WAAW,CAAC,kCAAkC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,6CAA6C,EAAE,KAAK,KAAK,CAAC,gGAAgG,CAAC;;8EAC/Q,6LAAC;8EAAK;;;;;;8EACN,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uCAvCvB;;;;;;;;;;;;;;;;kCAiDb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAgN;;;;;;0CAGlO,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;0BAKtC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAhNM;KAAA;uCAkNS", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/PricingSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { Check, Star, Zap, Crown, Rocket } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst PricingSection = () => {\n  const sectionRef = useRef(null);\n  const cardsRef = useRef([]);\n  const [billingCycle, setBillingCycle] = useState('monthly');\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial state\n      gsap.set(cardsRef.current, {\n        opacity: 0,\n        y: 50,\n        rotationY: 15\n      });\n\n      // Entrance animation\n      ScrollTrigger.create({\n        trigger: sectionRef.current,\n        start: \"top 80%\",\n        onEnter: () => {\n          gsap.to(cardsRef.current, {\n            opacity: 1,\n            y: 0,\n            rotationY: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n          });\n        }\n      });\n\n      // Hover animations\n      cardsRef.current.forEach(card => {\n        if (card) {\n          card.addEventListener('mouseenter', () => {\n            gsap.to(card, {\n              scale: 1.05,\n              rotationY: -5,\n              z: 50,\n              duration: 0.3,\n              ease: \"power2.out\"\n            });\n          });\n\n          card.addEventListener('mouseleave', () => {\n            gsap.to(card, {\n              scale: 1,\n              rotationY: 0,\n              z: 0,\n              duration: 0.3,\n              ease: \"power2.out\"\n            });\n          });\n        }\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToCardsRefs = (el) => {\n    if (el && !cardsRef.current.includes(el)) {\n      cardsRef.current.push(el);\n    }\n  };\n\n  const plans = [\n    {\n      name: \"Free\",\n      icon: <Star className=\"w-6 h-6\" />,\n      price: { monthly: 0, yearly: 0 },\n      description: \"Perfect for getting started with warranty tracking\",\n      features: [\n        \"5 items per month\",\n        \"Basic receipt scanning\",\n        \"Simple reminders\",\n        \"Mobile app access\",\n        \"Cloud storage (1GB)\"\n      ],\n      limitations: [\n        \"Limited AI features\",\n        \"Basic support\"\n      ],\n      color: \"white\",\n      popular: false,\n      cta: \"Start Free\"\n    },\n    {\n      name: \"Pro\",\n      icon: <Zap className=\"w-6 h-6\" />,\n      price: { monthly: 9.99, yearly: 99.99 },\n      description: \"For individuals and families who want full control\",\n      features: [\n        \"Unlimited items\",\n        \"Advanced AI extraction\",\n        \"Smart reminders\",\n        \"Email integration\",\n        \"Calendar sync\",\n        \"Priority support\",\n        \"Cloud storage (10GB)\",\n        \"Export capabilities\"\n      ],\n      limitations: [],\n      color: \"primary\",\n      popular: true,\n      cta: \"Start Pro Trial\"\n    },\n    {\n      name: \"Premium\",\n      icon: <Crown className=\"w-6 h-6\" />,\n      price: { monthly: 19.99, yearly: 199.99 },\n      description: \"Advanced features for power users and families\",\n      features: [\n        \"Everything in Pro\",\n        \"3D/AR inventory view\",\n        \"Advanced analytics\",\n        \"Family sharing (5 users)\",\n        \"Custom categories\",\n        \"API access\",\n        \"White-label options\",\n        \"Cloud storage (100GB)\",\n        \"Phone support\"\n      ],\n      limitations: [],\n      color: \"secondary\",\n      popular: false,\n      cta: \"Start Premium\"\n    },\n    {\n      name: \"Business\",\n      icon: <Rocket className=\"w-6 h-6\" />,\n      price: { monthly: 99, yearly: 999 },\n      description: \"For businesses managing multiple assets and teams\",\n      features: [\n        \"Everything in Premium\",\n        \"Unlimited team members\",\n        \"Advanced reporting\",\n        \"Custom integrations\",\n        \"Dedicated support\",\n        \"SLA guarantee\",\n        \"Custom training\",\n        \"Unlimited storage\",\n        \"On-premise option\"\n      ],\n      limitations: [],\n      color: \"accent\",\n      popular: false,\n      cta: \"Contact Sales\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">Simple, Transparent</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Pricing\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto mb-8\">\n            Choose the plan that fits your needs. Start free, upgrade anytime.\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center space-x-4\">\n            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-white' : 'text-white/60'}`}>\n              Monthly\n            </span>\n            <button\n              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}\n              className=\"relative w-14 h-7 bg-surface rounded-full border border-primary/30 transition-all duration-300\"\n            >\n              <div className={`absolute top-1 w-5 h-5 bg-primary rounded-full transition-all duration-300 ${\n                billingCycle === 'yearly' ? 'left-8' : 'left-1'\n              }`}></div>\n            </button>\n            <span className={`text-sm ${billingCycle === 'yearly' ? 'text-white' : 'text-white/60'}`}>\n              Yearly\n            </span>\n            {billingCycle === 'yearly' && (\n              <span className=\"text-xs bg-accent/20 text-accent px-2 py-1 rounded-full\">\n                Save 17%\n              </span>\n            )}\n          </div>\n        </div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid lg:grid-cols-4 gap-6\">\n          {plans.map((plan, index) => (\n            <div\n              key={index}\n              ref={addToCardsRefs}\n              className={`relative group perspective-1000 ${plan.popular ? 'lg:-mt-4' : ''}`}\n            >\n              {/* Popular Badge */}\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                  <div className=\"bg-gradient-to-r from-primary to-secondary px-4 py-1 rounded-full text-white text-sm font-semibold\">\n                    Most Popular\n                  </div>\n                </div>\n              )}\n\n              {/* Card */}\n              <div className={`glass rounded-lg p-6 h-full border-2 transition-all duration-300 ${\n                plan.popular \n                  ? 'border-primary glow-primary' \n                  : `border-${plan.color}/20 hover:border-${plan.color}/40`\n              } ${plan.popular ? 'lg:py-8' : ''}`}>\n                \n                {/* Header */}\n                <div className=\"text-center mb-6\">\n                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-br from-${plan.color}/20 to-transparent border border-${plan.color}/30 flex items-center justify-center mx-auto mb-4`}>\n                    <div className={`text-${plan.color === 'white' ? 'white' : plan.color}`}>\n                      {plan.icon}\n                    </div>\n                  </div>\n                  <h3 className=\"font-heading text-xl font-bold text-white mb-2\">\n                    {plan.name}\n                  </h3>\n                  <p className=\"text-white/70 text-sm\">\n                    {plan.description}\n                  </p>\n                </div>\n\n                {/* Price */}\n                <div className=\"text-center mb-6\">\n                  <div className=\"flex items-baseline justify-center\">\n                    <span className=\"text-3xl font-bold text-white\">\n                      ${plan.price[billingCycle]}\n                    </span>\n                    <span className=\"text-white/60 ml-1\">\n                      /{billingCycle === 'monthly' ? 'mo' : 'yr'}\n                    </span>\n                  </div>\n                  {billingCycle === 'yearly' && plan.price.monthly > 0 && (\n                    <div className=\"text-sm text-white/60 mt-1\">\n                      ${(plan.price.yearly / 12).toFixed(2)}/month billed yearly\n                    </div>\n                  )}\n                </div>\n\n                {/* Features */}\n                <div className=\"space-y-3 mb-6\">\n                  {plan.features.map((feature, featureIndex) => (\n                    <div key={featureIndex} className=\"flex items-start space-x-3\">\n                      <Check className={`w-4 h-4 mt-0.5 text-${plan.color === 'white' ? 'accent' : plan.color} flex-shrink-0`} />\n                      <span className=\"text-white/80 text-sm\">{feature}</span>\n                    </div>\n                  ))}\n                  {plan.limitations.map((limitation, limitIndex) => (\n                    <div key={limitIndex} className=\"flex items-start space-x-3 opacity-60\">\n                      <div className=\"w-4 h-4 mt-0.5 border border-white/30 rounded-sm flex-shrink-0\"></div>\n                      <span className=\"text-white/60 text-sm\">{limitation}</span>\n                    </div>\n                  ))}\n                </div>\n\n                {/* CTA Button */}\n                <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 ${\n                  plan.popular\n                    ? 'bg-gradient-to-r from-primary to-secondary text-white hover:shadow-lg hover:shadow-primary/25 glow-primary'\n                    : plan.color === 'white'\n                    ? 'border border-white/30 text-white hover:bg-white/10'\n                    : `bg-gradient-to-r from-${plan.color}/20 to-${plan.color}/10 border border-${plan.color}/30 text-white hover:bg-${plan.color}/30`\n                }`}>\n                  {plan.cta}\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom Features */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"font-heading text-2xl font-bold text-white mb-8\">\n            All plans include:\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">30-day free trial</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">Cancel anytime</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">24/7 customer support</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary/10 to-transparent rounded-full blur-xl animate-pulse delay-1000\"></div>\n    </section>\n  );\n};\n\nexport default PricingSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,iBAAiB;;IACrB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;gDAAC;oBACvB,gBAAgB;oBAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;wBACzB,SAAS;wBACT,GAAG;wBACH,WAAW;oBACb;oBAEA,qBAAqB;oBACrB,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS,WAAW,OAAO;wBAC3B,OAAO;wBACP,OAAO;4DAAE;gCACP,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;oCACxB,SAAS;oCACT,GAAG;oCACH,WAAW;oCACX,UAAU;oCACV,SAAS;oCACT,MAAM;gCACR;4BACF;;oBACF;oBAEA,mBAAmB;oBACnB,SAAS,OAAO,CAAC,OAAO;wDAAC,CAAA;4BACvB,IAAI,MAAM;gCACR,KAAK,gBAAgB,CAAC;oEAAc;wCAClC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;4CACZ,OAAO;4CACP,WAAW,CAAC;4CACZ,GAAG;4CACH,UAAU;4CACV,MAAM;wCACR;oCACF;;gCAEA,KAAK,gBAAgB,CAAC;oEAAc;wCAClC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;4CACZ,OAAO;4CACP,WAAW;4CACX,GAAG;4CACH,UAAU;4CACV,MAAM;wCACR;oCACF;;4BACF;wBACF;;gBAEF;+CAAG;YAEH;4CAAO,IAAM,IAAI,MAAM;;QACzB;mCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK;YACxC,SAAS,OAAO,CAAC,IAAI,CAAC;QACxB;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;gBAAE,SAAS;gBAAG,QAAQ;YAAE;YAC/B,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;gBAAE,SAAS;gBAAM,QAAQ;YAAM;YACtC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAO;YACxC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;gBAAE,SAAS;gBAAI,QAAQ;YAAI;YAClC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,YAAY,eAAe,iBAAiB;kDAAE;;;;;;kDAG3F,6LAAC;wCACC,SAAS,IAAM,gBAAgB,iBAAiB,YAAY,WAAW;wCACvE,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAW,CAAC,2EAA2E,EAC1F,iBAAiB,WAAW,WAAW,UACvC;;;;;;;;;;;kDAEJ,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,WAAW,eAAe,iBAAiB;kDAAE;;;;;;oCAGzF,iBAAiB,0BAChB,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;;;;;;;kCAQhF,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,KAAK;gCACL,WAAW,CAAC,gCAAgC,EAAE,KAAK,OAAO,GAAG,aAAa,IAAI;;oCAG7E,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAAqG;;;;;;;;;;;kDAOxH,6LAAC;wCAAI,WAAW,CAAC,iEAAiE,EAChF,KAAK,OAAO,GACR,gCACA,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAC5D,CAAC,EAAE,KAAK,OAAO,GAAG,YAAY,IAAI;;0DAGjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,4CAA4C,EAAE,KAAK,KAAK,CAAC,iCAAiC,EAAE,KAAK,KAAK,CAAC,iDAAiD,CAAC;kEACxK,cAAA,6LAAC;4DAAI,WAAW,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,UAAU,UAAU,KAAK,KAAK,EAAE;sEACpE,KAAK,IAAI;;;;;;;;;;;kEAGd,6LAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAgC;oEAC5C,KAAK,KAAK,CAAC,aAAa;;;;;;;0EAE5B,6LAAC;gEAAK,WAAU;;oEAAqB;oEACjC,iBAAiB,YAAY,OAAO;;;;;;;;;;;;;oDAGzC,iBAAiB,YAAY,KAAK,KAAK,CAAC,OAAO,GAAG,mBACjD,6LAAC;wDAAI,WAAU;;4DAA6B;4DACxC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAM5C,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;4DAAuB,WAAU;;8EAChC,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,KAAK,UAAU,WAAW,KAAK,KAAK,CAAC,cAAc,CAAC;;;;;;8EACvG,6LAAC;oEAAK,WAAU;8EAAyB;;;;;;;2DAFjC;;;;;oDAKX,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,2BACjC,6LAAC;4DAAqB,WAAU;;8EAC9B,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAyB;;;;;;;2DAFjC;;;;;;;;;;;0DAQd,6LAAC;gDAAO,WAAW,CAAC,2FAA2F,EAC7G,KAAK,OAAO,GACR,+GACA,KAAK,KAAK,KAAK,UACf,wDACA,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,EACpI;0DACC,KAAK,GAAG;;;;;;;;;;;;;+BA5ER;;;;;;;;;;kCAoFX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAjTM;KAAA;uCAmTS", "debugId": null}}, {"offset": {"line": 3042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApNM;uCAsNS", "debugId": null}}]}