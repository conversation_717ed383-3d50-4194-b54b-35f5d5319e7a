[{"name": "hot-reloader", "duration": 69, "timestamp": 3289086007, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1748604536276, "traceId": "99227aa1278ec967"}, {"name": "setup-dev-bundler", "duration": 1343754, "timestamp": 3288095292, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748604535285, "traceId": "99227aa1278ec967"}, {"name": "run-instrumentation-hook", "duration": 16, "timestamp": 3289469375, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748604536660, "traceId": "99227aa1278ec967"}, {"name": "start-dev-server", "duration": 1748154, "timestamp": 3287733463, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "56103899136", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "167870464", "memory.heapTotal": "112168960", "memory.heapUsed": "57548888"}, "startTime": 1748604534924, "traceId": "99227aa1278ec967"}, {"name": "compile-path", "duration": 2015718, "timestamp": 3310897895, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748604558088, "traceId": "99227aa1278ec967"}, {"name": "ensure-page", "duration": 2016766, "timestamp": 3310897350, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748604558088, "traceId": "99227aa1278ec967"}]