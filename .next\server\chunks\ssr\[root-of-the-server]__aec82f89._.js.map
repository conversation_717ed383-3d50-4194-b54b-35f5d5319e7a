{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,8OAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,8OAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/app/signup/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Navigation from '@/components/ui/Navigation';\nimport Footer from '@/components/ui/Footer';\nimport { \n  User, \n  Mail, \n  Lock, \n  Eye, \n  EyeOff, \n  Check, \n  Star, \n  Zap, \n  Crown,\n  Shield,\n  CreditCard,\n  ArrowRight\n} from 'lucide-react';\n\nexport default function SignupPage() {\n  const [step, setStep] = useState(1); // 1: Account, 2: Plan, 3: Payment, 4: Success\n  const [showPassword, setShowPassword] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState('pro');\n  const [billingCycle, setBillingCycle] = useState('monthly');\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    agreeToTerms: false,\n    subscribeNewsletter: true\n  });\n\n  const plans = [\n    {\n      id: 'free',\n      name: 'Free',\n      icon: <Star className=\"w-6 h-6\" />,\n      price: { monthly: 0, yearly: 0 },\n      description: 'Perfect for getting started',\n      features: [\n        '5 items per month',\n        'Basic receipt scanning',\n        'Simple reminders',\n        'Mobile app access',\n        'Cloud storage (1GB)'\n      ],\n      popular: false,\n      color: 'white'\n    },\n    {\n      id: 'pro',\n      name: 'Pro',\n      icon: <Zap className=\"w-6 h-6\" />,\n      price: { monthly: 9.99, yearly: 99.99 },\n      description: 'For individuals and families',\n      features: [\n        'Unlimited items',\n        'Advanced AI extraction',\n        'Smart reminders',\n        'Email integration',\n        'Priority support',\n        'Cloud storage (10GB)'\n      ],\n      popular: true,\n      color: 'primary'\n    },\n    {\n      id: 'premium',\n      name: 'Premium',\n      icon: <Crown className=\"w-6 h-6\" />,\n      price: { monthly: 19.99, yearly: 199.99 },\n      description: 'Advanced features for power users',\n      features: [\n        'Everything in Pro',\n        '3D/AR inventory view',\n        'Advanced analytics',\n        'Family sharing (5 users)',\n        'API access',\n        'Cloud storage (100GB)'\n      ],\n      popular: false,\n      color: 'secondary'\n    }\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleNextStep = () => {\n    if (step < 4) {\n      setStep(step + 1);\n    }\n  };\n\n  const handlePrevStep = () => {\n    if (step > 1) {\n      setStep(step - 1);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Simulate account creation\n    setTimeout(() => {\n      setStep(4);\n    }, 1500);\n  };\n\n  const selectedPlanData = plans.find(plan => plan.id === selectedPlan);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-space via-surface to-space\">\n      <Navigation />\n      \n      {/* Header */}\n      <section className=\"pt-24 pb-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h1 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">Join</span>\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent ml-3\">\n              WarrantyAI\n            </span>\n          </h1>\n          <p className=\"text-xl text-white/80 mb-8\">\n            Start managing your warranties like a pro in just a few minutes\n          </p>\n\n          {/* Progress Steps */}\n          <div className=\"flex items-center justify-center space-x-4 mb-8\">\n            {[1, 2, 3, 4].map((stepNum) => (\n              <div key={stepNum} className=\"flex items-center\">\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300 ${\n                  stepNum < step ? 'bg-accent text-space' :\n                  stepNum === step ? 'bg-primary text-space' :\n                  'bg-white/20 text-white/60'\n                }`}>\n                  {stepNum < step ? <Check className=\"w-4 h-4\" /> : stepNum}\n                </div>\n                {stepNum < 4 && (\n                  <div className={`w-12 h-1 mx-2 transition-all duration-300 ${\n                    stepNum < step ? 'bg-accent' : 'bg-white/20'\n                  }`}></div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Main Content */}\n      <section className=\"pb-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"glass rounded-lg border border-white/10 overflow-hidden\">\n            \n            {/* Step 1: Account Creation */}\n            {step === 1 && (\n              <div className=\"p-8\">\n                <h2 className=\"font-heading text-2xl font-bold text-white mb-6 text-center\">\n                  Create Your Account\n                </h2>\n                \n                <form onSubmit={(e) => { e.preventDefault(); handleNextStep(); }} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-white/70 text-sm mb-2\">First Name</label>\n                      <div className=\"relative\">\n                        <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                        <input\n                          type=\"text\"\n                          name=\"firstName\"\n                          value={formData.firstName}\n                          onChange={handleInputChange}\n                          className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                          placeholder=\"Enter your first name\"\n                          required\n                        />\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-white/70 text-sm mb-2\">Last Name</label>\n                      <div className=\"relative\">\n                        <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                        <input\n                          type=\"text\"\n                          name=\"lastName\"\n                          value={formData.lastName}\n                          onChange={handleInputChange}\n                          className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                          placeholder=\"Enter your last name\"\n                          required\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-white/70 text-sm mb-2\">Email Address</label>\n                    <div className=\"relative\">\n                      <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                        placeholder=\"Enter your email address\"\n                        required\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-white/70 text-sm mb-2\">Password</label>\n                    <div className=\"relative\">\n                      <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                      <input\n                        type={showPassword ? 'text' : 'password'}\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleInputChange}\n                        className=\"w-full pl-10 pr-12 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                        placeholder=\"Create a strong password\"\n                        required\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowPassword(!showPassword)}\n                        className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60\"\n                      >\n                        {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                      </button>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-start space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        name=\"agreeToTerms\"\n                        checked={formData.agreeToTerms}\n                        onChange={handleInputChange}\n                        className=\"mt-1 w-4 h-4 text-primary bg-transparent border border-white/30 rounded focus:ring-primary focus:ring-2\"\n                        required\n                      />\n                      <span className=\"text-white/70 text-sm\">\n                        I agree to the <a href=\"/terms\" className=\"text-primary hover:underline\">Terms of Service</a> and <a href=\"/privacy\" className=\"text-primary hover:underline\">Privacy Policy</a>\n                      </span>\n                    </label>\n                    \n                    <label className=\"flex items-start space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        name=\"subscribeNewsletter\"\n                        checked={formData.subscribeNewsletter}\n                        onChange={handleInputChange}\n                        className=\"mt-1 w-4 h-4 text-primary bg-transparent border border-white/30 rounded focus:ring-primary focus:ring-2\"\n                      />\n                      <span className=\"text-white/70 text-sm\">\n                        Subscribe to our newsletter for updates and warranty tips\n                      </span>\n                    </label>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-gradient-to-r from-primary to-secondary py-3 rounded-lg text-white font-semibold hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n                  >\n                    Continue to Plan Selection\n                  </button>\n                </form>\n              </div>\n            )}\n\n            {/* Step 2: Plan Selection */}\n            {step === 2 && (\n              <div className=\"p-8\">\n                <h2 className=\"font-heading text-2xl font-bold text-white mb-6 text-center\">\n                  Choose Your Plan\n                </h2>\n\n                {/* Billing Toggle */}\n                <div className=\"flex items-center justify-center space-x-4 mb-8\">\n                  <span className={`text-sm ${billingCycle === 'monthly' ? 'text-white' : 'text-white/60'}`}>\n                    Monthly\n                  </span>\n                  <button\n                    onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}\n                    className=\"relative w-14 h-7 bg-surface rounded-full border border-primary/30 transition-all duration-300\"\n                  >\n                    <div className={`absolute top-1 w-5 h-5 bg-primary rounded-full transition-all duration-300 ${\n                      billingCycle === 'yearly' ? 'left-8' : 'left-1'\n                    }`}></div>\n                  </button>\n                  <span className={`text-sm ${billingCycle === 'yearly' ? 'text-white' : 'text-white/60'}`}>\n                    Yearly\n                  </span>\n                  {billingCycle === 'yearly' && (\n                    <span className=\"text-xs bg-accent/20 text-accent px-2 py-1 rounded-full\">\n                      Save 17%\n                    </span>\n                  )}\n                </div>\n\n                <div className=\"grid md:grid-cols-3 gap-6 mb-8\">\n                  {plans.map((plan) => (\n                    <div\n                      key={plan.id}\n                      onClick={() => setSelectedPlan(plan.id)}\n                      className={`p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 ${\n                        selectedPlan === plan.id\n                          ? 'border-primary bg-primary/10 glow-primary'\n                          : 'border-white/20 hover:border-white/40 bg-white/5'\n                      } ${plan.popular ? 'relative' : ''}`}\n                    >\n                      {plan.popular && (\n                        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                          <div className=\"bg-gradient-to-r from-primary to-secondary px-3 py-1 rounded-full text-white text-xs font-semibold\">\n                            Most Popular\n                          </div>\n                        </div>\n                      )}\n\n                      <div className=\"text-center\">\n                        <div className={`w-12 h-12 rounded-lg bg-${plan.color}/20 border border-${plan.color}/30 flex items-center justify-center mx-auto mb-4`}>\n                          <div className={`text-${plan.color === 'white' ? 'white' : plan.color}`}>\n                            {plan.icon}\n                          </div>\n                        </div>\n                        \n                        <h3 className=\"font-heading text-lg font-bold text-white mb-2\">{plan.name}</h3>\n                        <p className=\"text-white/70 text-sm mb-4\">{plan.description}</p>\n                        \n                        <div className=\"mb-4\">\n                          <div className=\"text-2xl font-bold text-white\">\n                            ${plan.price[billingCycle]}\n                          </div>\n                          <div className=\"text-white/60 text-sm\">\n                            /{billingCycle === 'monthly' ? 'month' : 'year'}\n                          </div>\n                        </div>\n\n                        <div className=\"space-y-2 text-left\">\n                          {plan.features.map((feature, index) => (\n                            <div key={index} className=\"flex items-center space-x-2 text-sm\">\n                              <Check className=\"w-3 h-3 text-accent flex-shrink-0\" />\n                              <span className=\"text-white/80\">{feature}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <button\n                    onClick={handlePrevStep}\n                    className=\"px-6 py-3 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n                  >\n                    Back\n                  </button>\n                  \n                  <button\n                    onClick={handleNextStep}\n                    className=\"px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg text-white font-semibold hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n                  >\n                    Continue to Payment\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Step 3: Payment (Simulated) */}\n            {step === 3 && (\n              <div className=\"p-8\">\n                <h2 className=\"font-heading text-2xl font-bold text-white mb-6 text-center\">\n                  Payment Information\n                </h2>\n\n                {/* Order Summary */}\n                <div className=\"bg-white/5 border border-white/10 rounded-lg p-6 mb-8\">\n                  <h3 className=\"font-semibold text-white mb-4\">Order Summary</h3>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-white/70\">{selectedPlanData?.name} Plan ({billingCycle})</span>\n                    <span className=\"text-white font-semibold\">\n                      ${selectedPlanData?.price[billingCycle]}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-lg font-bold text-white pt-2 border-t border-white/20\">\n                    <span>Total</span>\n                    <span>${selectedPlanData?.price[billingCycle]}</span>\n                  </div>\n                </div>\n\n                {/* Simulated Payment Form */}\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <label className=\"block text-white/70 text-sm mb-2\">Card Number</label>\n                    <div className=\"relative\">\n                      <CreditCard className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                      <input\n                        type=\"text\"\n                        placeholder=\"1234 5678 9012 3456\"\n                        className=\"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-white/70 text-sm mb-2\">Expiry Date</label>\n                      <input\n                        type=\"text\"\n                        placeholder=\"MM/YY\"\n                        className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-white/70 text-sm mb-2\">CVV</label>\n                      <input\n                        type=\"text\"\n                        placeholder=\"123\"\n                        className=\"w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 text-white/70 text-sm\">\n                    <Shield className=\"w-4 h-4 text-accent\" />\n                    <span>Your payment information is secure and encrypted</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <button\n                      type=\"button\"\n                      onClick={handlePrevStep}\n                      className=\"px-6 py-3 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n                    >\n                      Back\n                    </button>\n                    \n                    <button\n                      type=\"submit\"\n                      className=\"px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg text-white font-semibold hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n                    >\n                      Complete Purchase\n                    </button>\n                  </div>\n                </form>\n              </div>\n            )}\n\n            {/* Step 4: Success */}\n            {step === 4 && (\n              <div className=\"p-8 text-center\">\n                <div className=\"w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                  <Check className=\"w-8 h-8 text-space\" />\n                </div>\n                \n                <h2 className=\"font-heading text-2xl font-bold text-white mb-4\">\n                  Welcome to WarrantyAI!\n                </h2>\n                \n                <p className=\"text-white/80 mb-8\">\n                  Your account has been created successfully. You can now start managing your warranties like a pro.\n                </p>\n\n                <div className=\"space-y-4 mb-8\">\n                  <div className=\"flex items-center justify-center space-x-3 text-white/70\">\n                    <Check className=\"w-4 h-4 text-accent\" />\n                    <span>Account created and verified</span>\n                  </div>\n                  <div className=\"flex items-center justify-center space-x-3 text-white/70\">\n                    <Check className=\"w-4 h-4 text-accent\" />\n                    <span>{selectedPlanData?.name} plan activated</span>\n                  </div>\n                  <div className=\"flex items-center justify-center space-x-3 text-white/70\">\n                    <Check className=\"w-4 h-4 text-accent\" />\n                    <span>Welcome email sent</span>\n                  </div>\n                </div>\n\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-3 rounded-lg text-white font-semibold hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                    Go to Dashboard\n                  </button>\n                  <button className=\"border border-primary/50 px-8 py-3 rounded-lg text-white font-semibold hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n                    Take a Tour\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,8CAA8C;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,qBAAqB;IACvB;IAEA,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;gBAAE,SAAS;gBAAG,QAAQ;YAAE;YAC/B,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;gBAAE,SAAS;gBAAM,QAAQ;YAAM;YACtC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAO;YACxC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,OAAO;QACT;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,4BAA4B;QAC5B,WAAW;YACT,QAAQ;QACV,GAAG;IACL;IAEA,MAAM,mBAAmB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,8OAAC;oCAAK,WAAU;8CAA2F;;;;;;;;;;;;sCAI7G,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAW,CAAC,wGAAwG,EACvH,UAAU,OAAO,yBACjB,YAAY,OAAO,0BACnB,6BACA;sDACC,UAAU,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uDAAe;;;;;;wCAEnD,UAAU,mBACT,8OAAC;4CAAI,WAAW,CAAC,0CAA0C,EACzD,UAAU,OAAO,cAAc,eAC/B;;;;;;;mCAXI;;;;;;;;;;;;;;;;;;;;;0BAoBlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BAGZ,SAAS,mBACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAI5E,8OAAC;wCAAK,UAAU,CAAC;4CAAQ,EAAE,cAAc;4CAAI;wCAAkB;wCAAG,WAAU;;0DAC1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAmC;;;;;;0EACpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAO,SAAS,SAAS;wEACzB,UAAU;wEACV,WAAU;wEACV,aAAY;wEACZ,QAAQ;;;;;;;;;;;;;;;;;;kEAKd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAmC;;;;;;0EACpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAO,SAAS,QAAQ;wEACxB,UAAU;wEACV,WAAU;wEACV,aAAY;wEACZ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0DAMhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAmC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;;;;;;;;;;;;;0DAKd,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAmC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAM,eAAe,SAAS;gEAC9B,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;0EAEV,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,gBAAgB,CAAC;gEAChC,WAAU;0EAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAKtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,YAAY;gEAC9B,UAAU;gEACV,WAAU;gEACV,QAAQ;;;;;;0EAEV,8OAAC;gEAAK,WAAU;;oEAAwB;kFACvB,8OAAC;wEAAE,MAAK;wEAAS,WAAU;kFAA+B;;;;;;oEAAoB;kFAAK,8OAAC;wEAAE,MAAK;wEAAW,WAAU;kFAA+B;;;;;;;;;;;;;;;;;;kEAIlK,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,mBAAmB;gEACrC,UAAU;gEACV,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAM5C,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAQN,SAAS,mBACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAK5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,YAAY,eAAe,iBAAiB;0DAAE;;;;;;0DAG3F,8OAAC;gDACC,SAAS,IAAM,gBAAgB,iBAAiB,YAAY,WAAW;gDACvE,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAW,CAAC,2EAA2E,EAC1F,iBAAiB,WAAW,WAAW,UACvC;;;;;;;;;;;0DAEJ,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,WAAW,eAAe,iBAAiB;0DAAE;;;;;;4CAGzF,iBAAiB,0BAChB,8OAAC;gDAAK,WAAU;0DAA0D;;;;;;;;;;;;kDAM9E,8OAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gDAEC,SAAS,IAAM,gBAAgB,KAAK,EAAE;gDACtC,WAAW,CAAC,mEAAmE,EAC7E,iBAAiB,KAAK,EAAE,GACpB,8CACA,mDACL,CAAC,EAAE,KAAK,OAAO,GAAG,aAAa,IAAI;;oDAEnC,KAAK,OAAO,kBACX,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAqG;;;;;;;;;;;kEAMxH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,iDAAiD,CAAC;0EACrI,cAAA,8OAAC;oEAAI,WAAW,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,UAAU,UAAU,KAAK,KAAK,EAAE;8EACpE,KAAK,IAAI;;;;;;;;;;;0EAId,8OAAC;gEAAG,WAAU;0EAAkD,KAAK,IAAI;;;;;;0EACzE,8OAAC;gEAAE,WAAU;0EAA8B,KAAK,WAAW;;;;;;0EAE3D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAAgC;4EAC3C,KAAK,KAAK,CAAC,aAAa;;;;;;;kFAE5B,8OAAC;wEAAI,WAAU;;4EAAwB;4EACnC,iBAAiB,YAAY,UAAU;;;;;;;;;;;;;0EAI7C,8OAAC;gEAAI,WAAU;0EACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;wEAAgB,WAAU;;0FACzB,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,8OAAC;gFAAK,WAAU;0FAAiB;;;;;;;uEAFzB;;;;;;;;;;;;;;;;;+CArCX,KAAK,EAAE;;;;;;;;;;kDAgDlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAID,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;4BAQN,SAAS,mBACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAK5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAiB,kBAAkB;4DAAK;4DAAQ;4DAAa;;;;;;;kEAC7E,8OAAC;wDAAK,WAAU;;4DAA2B;4DACvC,kBAAkB,KAAK,CAAC,aAAa;;;;;;;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAK;4DAAE,kBAAkB,KAAK,CAAC,aAAa;;;;;;;;;;;;;;;;;;;kDAKjD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAmC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAmC;;;;;;0EACpD,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAmC;;;;;;0EACpD,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,WAAU;kEACX;;;;;;kEAID,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;4BASR,SAAS,mBACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAGnB,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAIhE,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAIlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM,kBAAkB;4DAAK;;;;;;;;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAAyL;;;;;;0DAG3M,8OAAC;gDAAO,WAAU;0DAAmJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjL,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}