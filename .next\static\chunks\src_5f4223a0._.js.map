{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApNM;uCAsNS", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/app/roadmap/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport Navigation from '@/components/ui/Navigation';\nimport Footer from '@/components/ui/Footer';\nimport { \n  CheckCircle, \n  Clock, \n  Calendar, \n  Zap, \n  Brain, \n  Globe, \n  Shield, \n  Smartphone,\n  Eye,\n  Users,\n  Building,\n  Rocket\n} from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nexport default function RoadmapPage() {\n  const [selectedPhase, setSelectedPhase] = useState('current');\n  const timelineRef = useRef(null);\n  const phaseRefs = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Timeline animation\n      gsap.set(phaseRefs.current, { opacity: 0, x: -50 });\n      \n      ScrollTrigger.create({\n        trigger: timelineRef.current,\n        start: \"top 80%\",\n        onEnter: () => {\n          gsap.to(phaseRefs.current, {\n            opacity: 1,\n            x: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n          });\n        }\n      });\n\n    }, []);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToPhaseRefs = (el) => {\n    if (el && !phaseRefs.current.includes(el)) {\n      phaseRefs.current.push(el);\n    }\n  };\n\n  const roadmapPhases = [\n    {\n      id: 'current',\n      title: 'MVP (Current)',\n      period: 'Q4 2024',\n      status: 'active',\n      progress: 85,\n      description: 'Core warranty management features with AI-powered receipt scanning',\n      features: [\n        { name: 'Receipt Upload & Scanning', status: 'completed', icon: <Smartphone className=\"w-4 h-4\" /> },\n        { name: 'AI Data Extraction', status: 'completed', icon: <Brain className=\"w-4 h-4\" /> },\n        { name: 'Warranty Dashboard', status: 'completed', icon: <Calendar className=\"w-4 h-4\" /> },\n        { name: 'Smart Reminders', status: 'in-progress', icon: <Clock className=\"w-4 h-4\" /> },\n        { name: 'Basic Claim Assistant', status: 'planned', icon: <Shield className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '5,000+',\n        accuracy: '99.9%',\n        items: '25,000+'\n      }\n    },\n    {\n      id: 'phase1',\n      title: 'Enhanced AI',\n      period: 'Q1 2025',\n      status: 'planned',\n      progress: 0,\n      description: 'Advanced AI capabilities and improved user experience',\n      features: [\n        { name: 'Email Integration', status: 'planned', icon: <Globe className=\"w-4 h-4\" /> },\n        { name: 'Voice Commands', status: 'planned', icon: <Smartphone className=\"w-4 h-4\" /> },\n        { name: 'Advanced OCR', status: 'planned', icon: <Eye className=\"w-4 h-4\" /> },\n        { name: 'Multi-language Support', status: 'planned', icon: <Globe className=\"w-4 h-4\" /> },\n        { name: 'Bulk Import Tools', status: 'planned', icon: <Zap className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '50,000+',\n        accuracy: '99.95%',\n        languages: '10+'\n      }\n    },\n    {\n      id: 'phase2',\n      title: '3D & AR Features',\n      period: 'Q2 2025',\n      status: 'planned',\n      progress: 0,\n      description: 'Immersive 3D inventory visualization and AR experiences',\n      features: [\n        { name: '3D Item Visualization', status: 'planned', icon: <Eye className=\"w-4 h-4\" /> },\n        { name: 'AR Room Mapping', status: 'planned', icon: <Smartphone className=\"w-4 h-4\" /> },\n        { name: 'Virtual Inventory Tours', status: 'planned', icon: <Globe className=\"w-4 h-4\" /> },\n        { name: 'Item Location Tracking', status: 'planned', icon: <Calendar className=\"w-4 h-4\" /> },\n        { name: 'Photo-to-3D Conversion', status: 'planned', icon: <Brain className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '200,000+',\n        '3d_models': '1,000+',\n        ar_accuracy: '95%+'\n      }\n    },\n    {\n      id: 'phase3',\n      title: 'Enterprise & API',\n      period: 'Q3 2025',\n      status: 'planned',\n      progress: 0,\n      description: 'Business features and third-party integrations',\n      features: [\n        { name: 'Team Management', status: 'planned', icon: <Users className=\"w-4 h-4\" /> },\n        { name: 'Enterprise Dashboard', status: 'planned', icon: <Building className=\"w-4 h-4\" /> },\n        { name: 'Public API', status: 'planned', icon: <Globe className=\"w-4 h-4\" /> },\n        { name: 'Retailer Integrations', status: 'planned', icon: <Building className=\"w-4 h-4\" /> },\n        { name: 'Advanced Analytics', status: 'planned', icon: <Brain className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '500,000+',\n        businesses: '1,000+',\n        integrations: '50+'\n      }\n    },\n    {\n      id: 'phase4',\n      title: 'Global Expansion',\n      period: 'Q4 2025',\n      status: 'planned',\n      progress: 0,\n      description: 'International markets and advanced features',\n      features: [\n        { name: 'Global Marketplace', status: 'planned', icon: <Globe className=\"w-4 h-4\" /> },\n        { name: 'Blockchain Verification', status: 'planned', icon: <Shield className=\"w-4 h-4\" /> },\n        { name: 'IoT Device Integration', status: 'planned', icon: <Smartphone className=\"w-4 h-4\" /> },\n        { name: 'AI Predictive Maintenance', status: 'planned', icon: <Brain className=\"w-4 h-4\" /> },\n        { name: 'Insurance Partnerships', status: 'planned', icon: <Building className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '1M+',\n        countries: '25+',\n        partnerships: '100+'\n      }\n    },\n    {\n      id: 'future',\n      title: 'Future Vision',\n      period: '2026+',\n      status: 'vision',\n      progress: 0,\n      description: 'Next-generation features and emerging technologies',\n      features: [\n        { name: 'AI Personal Assistant', status: 'vision', icon: <Brain className=\"w-4 h-4\" /> },\n        { name: 'Metaverse Integration', status: 'vision', icon: <Eye className=\"w-4 h-4\" /> },\n        { name: 'Quantum Security', status: 'vision', icon: <Shield className=\"w-4 h-4\" /> },\n        { name: 'Neural Interface', status: 'vision', icon: <Zap className=\"w-4 h-4\" /> },\n        { name: 'Autonomous Claims', status: 'vision', icon: <Rocket className=\"w-4 h-4\" /> }\n      ],\n      metrics: {\n        users: '10M+',\n        ai_accuracy: '99.99%',\n        automation: '100%'\n      }\n    }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'accent';\n      case 'in-progress': return 'primary';\n      case 'planned': return 'secondary';\n      case 'vision': return 'white';\n      default: return 'white';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'completed': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'in-progress': return <Clock className=\"w-4 h-4\" />;\n      case 'planned': return <Calendar className=\"w-4 h-4\" />;\n      case 'vision': return <Rocket className=\"w-4 h-4\" />;\n      default: return <Calendar className=\"w-4 h-4\" />;\n    }\n  };\n\n  const selectedPhaseData = roadmapPhases.find(phase => phase.id === selectedPhase);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-space via-surface to-space\">\n      <Navigation />\n      \n      {/* Hero Section */}\n      <section className=\"pt-24 pb-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h1 className=\"font-heading text-4xl md:text-6xl font-bold mb-6\">\n            <span className=\"text-white\">Product</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Roadmap\n            </span>\n          </h1>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n            Our journey to revolutionize warranty management with cutting-edge AI and immersive technologies\n          </p>\n        </div>\n      </section>\n\n      {/* Timeline Navigation */}\n      <section className=\"pb-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-wrap justify-center gap-2 mb-8\">\n            {roadmapPhases.map((phase) => (\n              <button\n                key={phase.id}\n                onClick={() => setSelectedPhase(phase.id)}\n                className={`px-4 py-2 rounded-full border transition-all duration-300 ${\n                  selectedPhase === phase.id\n                    ? 'bg-primary/20 border-primary text-primary'\n                    : 'border-white/20 text-white/70 hover:border-white/40 hover:text-white'\n                }`}\n              >\n                {phase.title}\n              </button>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Main Timeline */}\n      <section className=\"pb-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            \n            {/* Timeline Sidebar */}\n            <div ref={timelineRef} className=\"space-y-6\">\n              {roadmapPhases.map((phase, index) => (\n                <div\n                  key={phase.id}\n                  ref={addToPhaseRefs}\n                  onClick={() => setSelectedPhase(phase.id)}\n                  className={`p-4 rounded-lg border cursor-pointer transition-all duration-300 ${\n                    selectedPhase === phase.id\n                      ? 'border-primary bg-primary/10 glow-primary'\n                      : 'border-white/20 hover:border-white/40 bg-white/5'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className={`font-semibold ${\n                      selectedPhase === phase.id ? 'text-primary' : 'text-white'\n                    }`}>\n                      {phase.title}\n                    </h3>\n                    <span className={`text-xs px-2 py-1 rounded-full border ${\n                      phase.status === 'active' ? 'border-accent/30 text-accent bg-accent/10' :\n                      phase.status === 'planned' ? 'border-secondary/30 text-secondary bg-secondary/10' :\n                      'border-white/30 text-white/70 bg-white/5'\n                    }`}>\n                      {phase.period}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-white/70 text-sm mb-3\">{phase.description}</p>\n                  \n                  {phase.progress > 0 && (\n                    <div className=\"mb-2\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <span className=\"text-xs text-white/60\">Progress</span>\n                        <span className=\"text-xs text-white/60\">{phase.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-white/20 rounded-full h-1\">\n                        <div \n                          className=\"bg-gradient-to-r from-primary to-accent h-1 rounded-full transition-all duration-500\"\n                          style={{ width: `${phase.progress}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  )}\n                  \n                  <div className=\"flex items-center space-x-2 text-xs text-white/60\">\n                    {getStatusIcon(phase.status)}\n                    <span className=\"capitalize\">{phase.status}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Phase Details */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"glass rounded-lg border border-white/10 overflow-hidden\">\n                \n                {/* Phase Header */}\n                <div className=\"bg-surface/50 border-b border-white/10 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"font-heading text-2xl font-bold text-white\">\n                      {selectedPhaseData?.title}\n                    </h2>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 rounded-full border text-sm ${\n                        selectedPhaseData?.status === 'active' ? 'border-accent/30 text-accent bg-accent/10' :\n                        selectedPhaseData?.status === 'planned' ? 'border-secondary/30 text-secondary bg-secondary/10' :\n                        'border-white/30 text-white/70 bg-white/5'\n                      }`}>\n                        {selectedPhaseData?.period}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-white/80 mb-4\">{selectedPhaseData?.description}</p>\n                  \n                  {selectedPhaseData?.progress > 0 && (\n                    <div>\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <span className=\"text-sm text-white/70\">Overall Progress</span>\n                        <span className=\"text-sm text-white/70\">{selectedPhaseData.progress}%</span>\n                      </div>\n                      <div className=\"w-full bg-white/20 rounded-full h-2\">\n                        <div \n                          className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-500\"\n                          style={{ width: `${selectedPhaseData.progress}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Features List */}\n                <div className=\"p-6\">\n                  <h3 className=\"font-semibold text-white mb-4\">Key Features</h3>\n                  <div className=\"space-y-3\">\n                    {selectedPhaseData?.features.map((feature, index) => (\n                      <div key={index} className={`flex items-center space-x-3 p-3 rounded-lg border ${\n                        feature.status === 'completed' ? 'border-accent/30 bg-accent/10' :\n                        feature.status === 'in-progress' ? 'border-primary/30 bg-primary/10' :\n                        'border-white/20 bg-white/5'\n                      }`}>\n                        <div className={`text-${getStatusColor(feature.status)}`}>\n                          {feature.icon}\n                        </div>\n                        <div className=\"flex-1\">\n                          <span className=\"text-white font-medium\">{feature.name}</span>\n                        </div>\n                        <div className={`text-${getStatusColor(feature.status)}`}>\n                          {getStatusIcon(feature.status)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Metrics */}\n                <div className=\"p-6 border-t border-white/10\">\n                  <h3 className=\"font-semibold text-white mb-4\">Target Metrics</h3>\n                  <div className=\"grid grid-cols-3 gap-4\">\n                    {Object.entries(selectedPhaseData?.metrics || {}).map(([key, value]) => (\n                      <div key={key} className=\"text-center p-3 bg-white/5 rounded-lg\">\n                        <div className=\"text-lg font-bold text-primary mb-1\">{value}</div>\n                        <div className=\"text-white/70 text-sm capitalize\">{key.replace('_', ' ')}</div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-surface/30\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-6\">\n            Be Part of the Journey\n          </h2>\n          <p className=\"text-xl text-white/80 mb-8\">\n            Join our community and help shape the future of warranty management\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n              Join Beta Program\n            </button>\n            <button className=\"border border-primary/50 px-8 py-4 rounded-full text-white font-semibold text-lg hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n              Subscribe to Updates\n            </button>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAsBA,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;6CAAC;oBACvB,qBAAqB;oBACrB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAEjD,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS,YAAY,OAAO;wBAC5B,OAAO;wBACP,OAAO;yDAAE;gCACP,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,UAAU,OAAO,EAAE;oCACzB,SAAS;oCACT,GAAG;oCACH,UAAU;oCACV,SAAS;oCACT,MAAM;gCACR;4BACF;;oBACF;gBAEF;4CAAG,EAAE;YAEL;yCAAO,IAAM,IAAI,MAAM;;QACzB;gCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,KAAK;YACzC,UAAU,OAAO,CAAC,IAAI,CAAC;QACzB;IACF;IAEA,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAA6B,QAAQ;oBAAa,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAAa;gBACnG;oBAAE,MAAM;oBAAsB,QAAQ;oBAAa,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACvF;oBAAE,MAAM;oBAAsB,QAAQ;oBAAa,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBAAa;gBAC1F;oBAAE,MAAM;oBAAmB,QAAQ;oBAAe,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACtF;oBAAE,MAAM;oBAAyB,QAAQ;oBAAW,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;gBAAa;aAC1F;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAAqB,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACpF;oBAAE,MAAM;oBAAkB,QAAQ;oBAAW,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAAa;gBACtF;oBAAE,MAAM;oBAAgB,QAAQ;oBAAW,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;gBAAa;gBAC7E;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACzF;oBAAE,MAAM;oBAAqB,QAAQ;oBAAW,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;gBAAa;aACnF;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAAyB,QAAQ;oBAAW,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;gBAAa;gBACtF;oBAAE,MAAM;oBAAmB,QAAQ;oBAAW,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAAa;gBACvF;oBAAE,MAAM;oBAA2B,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBAC1F;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBAAa;gBAC5F;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;aAC1F;YACD,SAAS;gBACP,OAAO;gBACP,aAAa;gBACb,aAAa;YACf;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAAmB,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBAClF;oBAAE,MAAM;oBAAwB,QAAQ;oBAAW,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBAAa;gBAC1F;oBAAE,MAAM;oBAAc,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBAC7E;oBAAE,MAAM;oBAAyB,QAAQ;oBAAW,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBAAa;gBAC3F;oBAAE,MAAM;oBAAsB,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;aACtF;YACD,SAAS;gBACP,OAAO;gBACP,YAAY;gBACZ,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAAsB,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACrF;oBAAE,MAAM;oBAA2B,QAAQ;oBAAW,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;gBAAa;gBAC3F;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAAa;gBAC9F;oBAAE,MAAM;oBAA6B,QAAQ;oBAAW,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBAC5F;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBAAa;aAC7F;YACD,SAAS;gBACP,OAAO;gBACP,WAAW;gBACX,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,aAAa;YACb,UAAU;gBACR;oBAAE,MAAM;oBAAyB,QAAQ;oBAAU,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBAAa;gBACvF;oBAAE,MAAM;oBAAyB,QAAQ;oBAAU,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;gBAAa;gBACrF;oBAAE,MAAM;oBAAoB,QAAQ;oBAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;gBAAa;gBACnF;oBAAE,MAAM;oBAAoB,QAAQ;oBAAU,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;gBAAa;gBAChF;oBAAE,MAAM;oBAAqB,QAAQ;oBAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;gBAAa;aACrF;YACD,SAAS;gBACP,OAAO;gBACP,aAAa;gBACb,YAAY;YACd;QACF;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAe,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC5C,KAAK;gBAAW,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAU,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC;gBAAS,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,oBAAoB,cAAc,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAEnE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;;sCAIxG,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAO3D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,MAAM,EAAE;gCACxC,WAAW,CAAC,0DAA0D,EACpE,kBAAkB,MAAM,EAAE,GACtB,8CACA,wEACJ;0CAED,MAAM,KAAK;+BARP,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;0BAgBvB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,KAAK;gCAAa,WAAU;0CAC9B,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;wCAEC,KAAK;wCACL,SAAS,IAAM,iBAAiB,MAAM,EAAE;wCACxC,WAAW,CAAC,iEAAiE,EAC3E,kBAAkB,MAAM,EAAE,GACtB,8CACA,oDACJ;;0DAEF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAW,CAAC,cAAc,EAC5B,kBAAkB,MAAM,EAAE,GAAG,iBAAiB,cAC9C;kEACC,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,MAAM,MAAM,KAAK,WAAW,8CAC5B,MAAM,MAAM,KAAK,YAAY,uDAC7B,4CACA;kEACC,MAAM,MAAM;;;;;;;;;;;;0DAIjB,6LAAC;gDAAE,WAAU;0DAA8B,MAAM,WAAW;;;;;;4CAE3D,MAAM,QAAQ,GAAG,mBAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEAAyB,MAAM,QAAQ;oEAAC;;;;;;;;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;0DAM7C,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,MAAM,MAAM;kEAC3B,6LAAC;wDAAK,WAAU;kEAAc,MAAM,MAAM;;;;;;;;;;;;;uCA3CvC,MAAM,EAAE;;;;;;;;;;0CAkDnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAGb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,mBAAmB;;;;;;sEAEtB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,sCAAsC,EACtD,mBAAmB,WAAW,WAAW,8CACzC,mBAAmB,WAAW,YAAY,uDAC1C,4CACA;0EACC,mBAAmB;;;;;;;;;;;;;;;;;8DAK1B,6LAAC;oDAAE,WAAU;8DAAsB,mBAAmB;;;;;;gDAErD,mBAAmB,WAAW,mBAC7B,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAU;;wEAAyB,kBAAkB,QAAQ;wEAAC;;;;;;;;;;;;;sEAEtE,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,kBAAkB,QAAQ,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;;;;;;;sDAQ3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,SAAS,IAAI,CAAC,SAAS,sBACzC,6LAAC;4DAAgB,WAAW,CAAC,kDAAkD,EAC7E,QAAQ,MAAM,KAAK,cAAc,kCACjC,QAAQ,MAAM,KAAK,gBAAgB,oCACnC,8BACA;;8EACA,6LAAC;oEAAI,WAAW,CAAC,KAAK,EAAE,eAAe,QAAQ,MAAM,GAAG;8EACrD,QAAQ,IAAI;;;;;;8EAEf,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFAA0B,QAAQ,IAAI;;;;;;;;;;;8EAExD,6LAAC;oEAAI,WAAW,CAAC,KAAK,EAAE,eAAe,QAAQ,MAAM,GAAG;8EACrD,cAAc,QAAQ,MAAM;;;;;;;2DAZvB;;;;;;;;;;;;;;;;sDAoBhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,mBAAmB,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjE,6LAAC;4DAAc,WAAU;;8EACvB,6LAAC;oEAAI,WAAU;8EAAuC;;;;;;8EACtD,6LAAC;oEAAI,WAAU;8EAAoC,IAAI,OAAO,CAAC,KAAK;;;;;;;2DAF5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAmM;;;;;;8CAGrN,6LAAC;oCAAO,WAAU;8CAA6J;;;;;;;;;;;;;;;;;;;;;;;0BAOrL,6LAAC,oIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA/XwB;KAAA", "debugId": null}}]}