{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApNM;uCAsNS", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/app/why-us/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport Navigation from '@/components/ui/Navigation';\nimport Footer from '@/components/ui/Footer';\nimport { \n  Zap, \n  Brain, \n  Shield, \n  Globe, \n  Users, \n  Award,\n  TrendingUp,\n  Code,\n  Lightbulb,\n  Target,\n  Heart,\n  Star\n} from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nexport default function WhyUsPage() {\n  const heroRef = useRef(null);\n  const advantagesRef = useRef([]);\n  const teamRef = useRef([]);\n  const valuesRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Hero animation\n      gsap.fromTo(heroRef.current,\n        { opacity: 0, y: 50 },\n        { opacity: 1, y: 0, duration: 1, ease: \"power3.out\" }\n      );\n\n      // Stagger animations for sections\n      [advantagesRef, teamRef, valuesRef].forEach((ref) => {\n        if (ref.current.length > 0) {\n          gsap.set(ref.current, { opacity: 0, y: 50 });\n          \n          ScrollTrigger.create({\n            trigger: ref.current[0].parentElement,\n            start: \"top 80%\",\n            onEnter: () => {\n              gsap.to(ref.current, {\n                opacity: 1,\n                y: 0,\n                duration: 0.6,\n                stagger: 0.1,\n                ease: \"power3.out\"\n              });\n            }\n          });\n        }\n      });\n\n    }, []);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToAdvantagesRefs = (el) => {\n    if (el && !advantagesRef.current.includes(el)) {\n      advantagesRef.current.push(el);\n    }\n  };\n\n  const addToTeamRefs = (el) => {\n    if (el && !teamRef.current.includes(el)) {\n      teamRef.current.push(el);\n    }\n  };\n\n  const addToValuesRefs = (el) => {\n    if (el && !valuesRef.current.includes(el)) {\n      valuesRef.current.push(el);\n    }\n  };\n\n  const competitiveAdvantages = [\n    {\n      icon: <Brain className=\"w-8 h-8\" />,\n      title: \"AI-First Approach\",\n      description: \"Built from the ground up with advanced machine learning, not retrofitted with basic automation.\",\n      stats: \"99.9% accuracy rate\",\n      color: \"primary\"\n    },\n    {\n      icon: <Globe className=\"w-8 h-8\" />,\n      title: \"Comprehensive Coverage\",\n      description: \"Only solution covering electronics, appliances, vehicles, and home systems in one platform.\",\n      stats: \"25+ item categories\",\n      color: \"secondary\"\n    },\n    {\n      icon: <Zap className=\"w-8 h-8\" />,\n      title: \"Real-time Intelligence\",\n      description: \"Proactive reminders and smart insights, not just passive storage of warranty information.\",\n      stats: \"< 2s processing time\",\n      color: \"accent\"\n    },\n    {\n      icon: <Shield className=\"w-8 h-8\" />,\n      title: \"Future-Ready Technology\",\n      description: \"3D/AR visualization and blockchain ownership proof for the next generation of asset management.\",\n      stats: \"Web3 compatible\",\n      color: \"primary\"\n    }\n  ];\n\n  const teamMembers = [\n    {\n      name: \"Alex Chen\",\n      role: \"CEO & Co-founder\",\n      background: \"Former Apple AI Engineer, Stanford CS\",\n      expertise: \"Machine Learning, Product Strategy\",\n      achievements: \"Led AI initiatives for 50M+ users\",\n      image: \"/team/alex.jpg\"\n    },\n    {\n      name: \"Sarah Kim\",\n      role: \"CTO & Co-founder\",\n      background: \"Ex-Google Cloud Architect, MIT\",\n      expertise: \"Distributed Systems, Security\",\n      achievements: \"Built systems handling 1B+ requests/day\",\n      image: \"/team/sarah.jpg\"\n    },\n    {\n      name: \"Marcus Rodriguez\",\n      role: \"Head of AI\",\n      background: \"Former Tesla Autopilot, PhD Computer Vision\",\n      expertise: \"Computer Vision, Deep Learning\",\n      achievements: \"20+ patents in AI/ML\",\n      image: \"/team/marcus.jpg\"\n    },\n    {\n      name: \"Emily Watson\",\n      role: \"Head of Design\",\n      background: \"Ex-Airbnb Design Lead, RISD\",\n      expertise: \"UX/UI, Design Systems\",\n      achievements: \"Designed for 100M+ users\",\n      image: \"/team/emily.jpg\"\n    }\n  ];\n\n  const coreValues = [\n    {\n      icon: <Heart className=\"w-6 h-6\" />,\n      title: \"User-Centric\",\n      description: \"Every decision starts with how it benefits our users\",\n      color: \"red-500\"\n    },\n    {\n      icon: <Lightbulb className=\"w-6 h-6\" />,\n      title: \"Innovation\",\n      description: \"Pushing boundaries with cutting-edge technology\",\n      color: \"yellow-500\"\n    },\n    {\n      icon: <Shield className=\"w-6 h-6\" />,\n      title: \"Trust & Security\",\n      description: \"Your data privacy and security is our top priority\",\n      color: \"green-500\"\n    },\n    {\n      icon: <Target className=\"w-6 h-6\" />,\n      title: \"Excellence\",\n      description: \"Delivering exceptional quality in everything we build\",\n      color: \"blue-500\"\n    }\n  ];\n\n  const achievements = [\n    { metric: \"99.9%\", label: \"AI Accuracy Rate\", icon: <Brain className=\"w-5 h-5\" /> },\n    { metric: \"< 2s\", label: \"Processing Time\", icon: <Zap className=\"w-5 h-5\" /> },\n    { metric: \"5,000+\", label: \"Beta Users\", icon: <Users className=\"w-5 h-5\" /> },\n    { metric: \"25,000+\", label: \"Items Tracked\", icon: <Award className=\"w-5 h-5\" /> }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-space via-surface to-space\">\n      <Navigation />\n      \n      {/* Hero Section */}\n      <section className=\"pt-24 pb-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div ref={heroRef} className=\"text-center\">\n            <h1 className=\"font-heading text-4xl md:text-6xl font-bold mb-6\">\n              <span className=\"text-white\">Why Choose</span>\n              <br />\n              <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n                WarrantyAI?\n              </span>\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto mb-8\">\n              We're not just another warranty tracker. We're building the future of intelligent \n              asset management with cutting-edge AI and user-centric design.\n            </p>\n            \n            {/* Achievement Stats */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12\">\n              {achievements.map((achievement, index) => (\n                <div key={index} className=\"text-center p-6 glass rounded-lg border border-white/10\">\n                  <div className=\"text-primary mb-2\">{achievement.icon}</div>\n                  <div className=\"text-2xl font-bold text-white mb-1\">{achievement.metric}</div>\n                  <div className=\"text-white/70 text-sm\">{achievement.label}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Competitive Advantages */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-4\">\n              Our Competitive Edge\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              What sets us apart from traditional warranty management solutions\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-8\">\n            {competitiveAdvantages.map((advantage, index) => (\n              <div\n                key={index}\n                ref={addToAdvantagesRefs}\n                className={`p-8 glass rounded-lg border border-${advantage.color}/20 hover:border-${advantage.color}/40 transition-all duration-300 group`}\n              >\n                <div className=\"flex items-start space-x-6\">\n                  <div className={`text-${advantage.color} group-hover:scale-110 transition-transform duration-300`}>\n                    {advantage.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-heading text-xl font-bold text-white mb-3\">\n                      {advantage.title}\n                    </h3>\n                    <p className=\"text-white/80 mb-4 leading-relaxed\">\n                      {advantage.description}\n                    </p>\n                    <div className={`inline-flex items-center px-3 py-1 bg-${advantage.color}/20 border border-${advantage.color}/30 rounded-full text-${advantage.color} text-sm font-medium`}>\n                      <Star className=\"w-3 h-3 mr-1\" />\n                      {advantage.stats}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Team Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-surface/30\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-4\">\n              World-Class Team\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              Experienced leaders from top tech companies building the future of warranty management\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {teamMembers.map((member, index) => (\n              <div\n                key={index}\n                ref={addToTeamRefs}\n                className=\"text-center p-6 glass rounded-lg border border-white/10 hover:border-primary/30 transition-all duration-300 group\"\n              >\n                <div className=\"w-24 h-24 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                  <Users className=\"w-8 h-8 text-primary\" />\n                </div>\n                <h3 className=\"font-semibold text-white text-lg mb-1\">{member.name}</h3>\n                <div className=\"text-primary text-sm mb-2\">{member.role}</div>\n                <div className=\"text-white/70 text-sm mb-3\">{member.background}</div>\n                <div className=\"text-white/60 text-xs mb-3\">{member.expertise}</div>\n                <div className=\"text-accent text-xs\">{member.achievements}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Core Values */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-4\">\n              Our Core Values\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              The principles that guide everything we do\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {coreValues.map((value, index) => (\n              <div\n                key={index}\n                ref={addToValuesRefs}\n                className=\"text-center p-6 glass rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300 group\"\n              >\n                <div className={`w-12 h-12 bg-${value.color}/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                  <div className={`text-${value.color}`}>\n                    {value.icon}\n                  </div>\n                </div>\n                <h3 className=\"font-semibold text-white mb-3\">{value.title}</h3>\n                <p className=\"text-white/70 text-sm leading-relaxed\">{value.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Technology Stack */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-surface/30\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-4\">\n              Cutting-Edge Technology\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              Built with the latest technologies for performance, scalability, and security\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center p-8 glass rounded-lg border border-white/10\">\n              <Code className=\"w-12 h-12 text-primary mx-auto mb-4\" />\n              <h3 className=\"font-semibold text-white text-lg mb-3\">AI & Machine Learning</h3>\n              <p className=\"text-white/70 text-sm mb-4\">\n                Advanced neural networks, computer vision, and natural language processing\n              </p>\n              <div className=\"space-y-1 text-xs text-white/60\">\n                <div>• TensorFlow & PyTorch</div>\n                <div>• OpenCV & Tesseract OCR</div>\n                <div>• Custom ML Models</div>\n              </div>\n            </div>\n\n            <div className=\"text-center p-8 glass rounded-lg border border-white/10\">\n              <Globe className=\"w-12 h-12 text-secondary mx-auto mb-4\" />\n              <h3 className=\"font-semibold text-white text-lg mb-3\">Cloud Infrastructure</h3>\n              <p className=\"text-white/70 text-sm mb-4\">\n                Scalable, secure, and globally distributed cloud architecture\n              </p>\n              <div className=\"space-y-1 text-xs text-white/60\">\n                <div>• AWS & Kubernetes</div>\n                <div>• Microservices Architecture</div>\n                <div>• Global CDN</div>\n              </div>\n            </div>\n\n            <div className=\"text-center p-8 glass rounded-lg border border-white/10\">\n              <Shield className=\"w-12 h-12 text-accent mx-auto mb-4\" />\n              <h3 className=\"font-semibold text-white text-lg mb-3\">Security & Privacy</h3>\n              <p className=\"text-white/70 text-sm mb-4\">\n                Enterprise-grade security with end-to-end encryption\n              </p>\n              <div className=\"space-y-1 text-xs text-white/60\">\n                <div>• AES-256 Encryption</div>\n                <div>• SOC 2 Compliance</div>\n                <div>• GDPR Ready</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Experience the Difference?\n          </h2>\n          <p className=\"text-xl text-white/80 mb-8\">\n            Join thousands of users who trust WarrantyAI to protect their valuable assets\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n              Start Free Trial\n            </button>\n            <button className=\"border border-primary/50 px-8 py-4 rounded-full text-white font-semibold text-lg hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n              Schedule Demo\n            </button>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAsBA,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC/B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACzB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;2CAAC;oBACvB,iBAAiB;oBACjB,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,EACzB;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAG,MAAM;oBAAa;oBAGtD,kCAAkC;oBAClC;wBAAC;wBAAe;wBAAS;qBAAU,CAAC,OAAO;mDAAC,CAAC;4BAC3C,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG;gCAC1B,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,IAAI,OAAO,EAAE;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAE1C,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;oCACnB,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,aAAa;oCACrC,OAAO;oCACP,OAAO;mEAAE;4CACP,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,IAAI,OAAO,EAAE;gDACnB,SAAS;gDACT,GAAG;gDACH,UAAU;gDACV,SAAS;gDACT,MAAM;4CACR;wCACF;;gCACF;4BACF;wBACF;;gBAEF;0CAAG,EAAE;YAEL;uCAAO,IAAM,IAAI,MAAM;;QACzB;8BAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,IAAI,MAAM,CAAC,cAAc,OAAO,CAAC,QAAQ,CAAC,KAAK;YAC7C,cAAc,OAAO,CAAC,IAAI,CAAC;QAC7B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,KAAK;YACvC,QAAQ,OAAO,CAAC,IAAI,CAAC;QACvB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,KAAK;YACzC,UAAU,OAAO,CAAC,IAAI,CAAC;QACzB;IACF;IAEA,MAAM,wBAAwB;QAC5B;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,WAAW;YACX,cAAc;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,WAAW;YACX,cAAc;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,WAAW;YACX,cAAc;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,YAAY;YACZ,WAAW;YACX,cAAc;YACd,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YAAE,QAAQ;YAAS,OAAO;YAAoB,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;QAClF;YAAE,QAAQ;YAAQ,OAAO;YAAmB,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QAAa;QAC9E;YAAE,QAAQ;YAAU,OAAO;YAAc,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;QAC7E;YAAE,QAAQ;YAAW,OAAO;YAAiB,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;KAClF;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,KAAK;wBAAS,WAAU;;0CAC3B,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,6LAAC;;;;;kDACD,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAM5D,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAqB,YAAY,IAAI;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAAsC,YAAY,MAAM;;;;;;0DACvE,6LAAC;gDAAI,WAAU;0DAAyB,YAAY,KAAK;;;;;;;uCAHjD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,WAAW,sBACrC,6LAAC;oCAEC,KAAK;oCACL,WAAW,CAAC,mCAAmC,EAAE,UAAU,KAAK,CAAC,iBAAiB,EAAE,UAAU,KAAK,CAAC,qCAAqC,CAAC;8CAE1I,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,KAAK,EAAE,UAAU,KAAK,CAAC,wDAAwD,CAAC;0DAC9F,UAAU,IAAI;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,UAAU,KAAK;;;;;;kEAElB,6LAAC;wDAAE,WAAU;kEACV,UAAU,WAAW;;;;;;kEAExB,6LAAC;wDAAI,WAAW,CAAC,sCAAsC,EAAE,UAAU,KAAK,CAAC,kBAAkB,EAAE,UAAU,KAAK,CAAC,sBAAsB,EAAE,UAAU,KAAK,CAAC,oBAAoB,CAAC;;0EACxK,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,UAAU,KAAK;;;;;;;;;;;;;;;;;;;mCAjBjB;;;;;;;;;;;;;;;;;;;;;0BA4Bf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC;oCAEC,KAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAAyC,OAAO,IAAI;;;;;;sDAClE,6LAAC;4CAAI,WAAU;sDAA6B,OAAO,IAAI;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDAA8B,OAAO,UAAU;;;;;;sDAC9D,6LAAC;4CAAI,WAAU;sDAA8B,OAAO,SAAS;;;;;;sDAC7D,6LAAC;4CAAI,WAAU;sDAAuB,OAAO,YAAY;;;;;;;mCAXpD;;;;;;;;;;;;;;;;;;;;;0BAmBf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC;oCAEC,KAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAW,CAAC,aAAa,EAAE,MAAM,KAAK,CAAC,oHAAoH,CAAC;sDAC/J,cAAA,6LAAC;gDAAI,WAAW,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;0DAClC,MAAM,IAAI;;;;;;;;;;;sDAGf,6LAAC;4CAAG,WAAU;sDAAiC,MAAM,KAAK;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAyC,MAAM,WAAW;;;;;;;mCAVlE;;;;;;;;;;;;;;;;;;;;;0BAkBf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;8CAIT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;8CAIT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAmM;;;;;;8CAGrN,6LAAC;oCAAO,WAAU;8CAA6J;;;;;;;;;;;;;;;;;;;;;;;0BAOrL,6LAAC,oIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAzXwB;KAAA", "debugId": null}}]}