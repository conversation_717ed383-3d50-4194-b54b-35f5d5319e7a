{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApNM;uCAsNS", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/app/pitch/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport Navigation from '@/components/ui/Navigation';\nimport Footer from '@/components/ui/Footer';\nimport { \n  ChevronLeft, \n  ChevronRight, \n  Play, \n  Pause, \n  RotateCcw,\n  TrendingUp,\n  Users,\n  DollarSign,\n  Target,\n  Zap,\n  Shield,\n  Globe\n} from 'lucide-react';\n\nexport default function PitchPage() {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isAutoPlay, setIsAutoPlay] = useState(false);\n  const slideRef = useRef(null);\n\n  const slides = [\n    {\n      id: 1,\n      title: \"The Problem\",\n      subtitle: \"Warranty Management is Broken\",\n      content: {\n        type: \"problem\",\n        stats: [\n          { value: \"$2.1B\", label: \"Unclaimed warranties annually\", color: \"red-500\" },\n          { value: \"70%\", label: \"People lose receipts\", color: \"red-500\" },\n          { value: \"25+\", label: \"Items per household\", color: \"yellow-500\" },\n          { value: \"0\", label: \"Unified solutions\", color: \"red-500\" }\n        ],\n        painPoints: [\n          \"Lost receipts and warranty documents\",\n          \"Forgotten expiration dates\",\n          \"Complex claim processes\",\n          \"No centralized tracking system\"\n        ]\n      }\n    },\n    {\n      id: 2,\n      title: \"Market Opportunity\",\n      subtitle: \"$7.8B Market by 2030\",\n      content: {\n        type: \"market\",\n        marketSize: {\n          current: 4.2,\n          projected: 7.8,\n          growth: \"9.2% CAGR\"\n        },\n        segments: [\n          { name: \"Consumer Electronics\", value: 45, color: \"primary\" },\n          { name: \"Home Appliances\", value: 30, color: \"secondary\" },\n          { name: \"Automotive\", value: 15, color: \"accent\" },\n          { name: \"Other\", value: 10, color: \"white\" }\n        ]\n      }\n    },\n    {\n      id: 3,\n      title: \"Our Solution\",\n      subtitle: \"AI-Powered Warranty Management\",\n      content: {\n        type: \"solution\",\n        features: [\n          {\n            icon: <Zap className=\"w-8 h-8\" />,\n            title: \"AI Receipt Scanning\",\n            description: \"Instant warranty extraction with 99.9% accuracy\"\n          },\n          {\n            icon: <Shield className=\"w-8 h-8\" />,\n            title: \"Smart Reminders\",\n            description: \"Proactive notifications before expiration\"\n          },\n          {\n            icon: <Globe className=\"w-8 h-8\" />,\n            title: \"3D Inventory\",\n            description: \"Visualize items in AR/3D space\"\n          }\n        ]\n      }\n    },\n    {\n      id: 4,\n      title: \"Business Model\",\n      subtitle: \"Scalable SaaS Revenue Streams\",\n      content: {\n        type: \"business\",\n        tiers: [\n          { name: \"Free\", price: 0, users: \"100K+\", revenue: 0 },\n          { name: \"Pro\", price: 9.99, users: \"10K+\", revenue: 99900 },\n          { name: \"Premium\", price: 19.99, users: \"2K+\", revenue: 39980 },\n          { name: \"Business\", price: 99, users: \"50+\", revenue: 4950 }\n        ],\n        totalMRR: 144830\n      }\n    },\n    {\n      id: 5,\n      title: \"Competitive Analysis\",\n      subtitle: \"Clear Market Leadership\",\n      content: {\n        type: \"competition\",\n        competitors: [\n          { name: \"WarrantyAI\", ai: true, reminders: true, ar: true, claims: true, score: 95 },\n          { name: \"Bilt\", ai: false, reminders: false, ar: false, claims: false, score: 40 },\n          { name: \"Everdence\", ai: false, reminders: true, ar: false, claims: false, score: 50 },\n          { name: \"Sortly\", ai: false, reminders: false, ar: false, claims: false, score: 35 }\n        ]\n      }\n    },\n    {\n      id: 6,\n      title: \"Traction & Metrics\",\n      subtitle: \"Strong Early Adoption\",\n      content: {\n        type: \"traction\",\n        metrics: [\n          { label: \"Beta Users\", value: \"5,000+\", growth: \"+150%\" },\n          { label: \"Items Tracked\", value: \"25,000+\", growth: \"+300%\" },\n          { label: \"Accuracy Rate\", value: \"99.9%\", growth: \"+0.5%\" },\n          { label: \"User Retention\", value: \"85%\", growth: \"+15%\" }\n        ]\n      }\n    },\n    {\n      id: 7,\n      title: \"Financial Projections\",\n      subtitle: \"Path to $100M ARR\",\n      content: {\n        type: \"financials\",\n        projections: [\n          { year: \"2024\", users: \"50K\", revenue: \"500K\", expenses: \"300K\" },\n          { year: \"2025\", users: \"200K\", revenue: \"2.5M\", expenses: \"1.5M\" },\n          { year: \"2026\", users: \"500K\", revenue: \"8M\", expenses: \"4M\" },\n          { year: \"2027\", users: \"1M\", revenue: \"20M\", expenses: \"10M\" }\n        ]\n      }\n    },\n    {\n      id: 8,\n      title: \"Funding Ask\",\n      subtitle: \"$5M Series A\",\n      content: {\n        type: \"funding\",\n        ask: 5000000,\n        allocation: [\n          { category: \"Product Development\", percentage: 40, amount: 2000000 },\n          { category: \"Marketing & Sales\", percentage: 30, amount: 1500000 },\n          { category: \"Team Expansion\", percentage: 20, amount: 1000000 },\n          { category: \"Operations\", percentage: 10, amount: 500000 }\n        ],\n        milestones: [\n          \"Launch enterprise features\",\n          \"Expand to 5 new markets\",\n          \"Reach 1M active users\",\n          \"Achieve $20M ARR\"\n        ]\n      }\n    }\n  ];\n\n  useEffect(() => {\n    if (slideRef.current) {\n      gsap.fromTo(slideRef.current, \n        { opacity: 0, x: 50 },\n        { opacity: 1, x: 0, duration: 0.5, ease: \"power2.out\" }\n      );\n    }\n  }, [currentSlide]);\n\n  useEffect(() => {\n    let interval;\n    if (isAutoPlay) {\n      interval = setInterval(() => {\n        setCurrentSlide(prev => (prev + 1) % slides.length);\n      }, 5000);\n    }\n    return () => clearInterval(interval);\n  }, [isAutoPlay, slides.length]);\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);\n  };\n\n  const goToSlide = (index) => {\n    setCurrentSlide(index);\n  };\n\n  const currentSlideData = slides[currentSlide];\n\n  const renderSlideContent = () => {\n    const { content } = currentSlideData;\n\n    switch (content.type) {\n      case \"problem\":\n        return (\n          <div className=\"space-y-8\">\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6\">\n              {content.stats.map((stat, index) => (\n                <div key={index} className={`text-center p-6 bg-white/5 border border-${stat.color}/20 rounded-lg`}>\n                  <div className={`text-3xl font-bold text-${stat.color} mb-2`}>{stat.value}</div>\n                  <div className=\"text-white/70 text-sm\">{stat.label}</div>\n                </div>\n              ))}\n            </div>\n            <div className=\"space-y-4\">\n              {content.painPoints.map((point, index) => (\n                <div key={index} className=\"flex items-center space-x-3 p-4 bg-red-500/10 border border-red-500/20 rounded-lg\">\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n                  <span className=\"text-white\">{point}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        );\n\n      case \"market\":\n        return (\n          <div className=\"space-y-8\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl font-bold text-primary mb-4\">${content.marketSize.projected}B</div>\n              <div className=\"text-xl text-white/80\">Market size by 2030</div>\n              <div className=\"text-accent\">{content.marketSize.growth}</div>\n            </div>\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              {content.segments.map((segment, index) => (\n                <div key={index} className={`text-center p-4 bg-${segment.color}/10 border border-${segment.color}/20 rounded-lg`}>\n                  <div className={`text-2xl font-bold text-${segment.color} mb-2`}>{segment.value}%</div>\n                  <div className=\"text-white/70 text-sm\">{segment.name}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        );\n\n      case \"solution\":\n        return (\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {content.features.map((feature, index) => (\n              <div key={index} className=\"text-center p-6 bg-white/5 border border-white/10 rounded-lg hover:border-primary/30 transition-all duration-300\">\n                <div className=\"text-primary mb-4\">{feature.icon}</div>\n                <h3 className=\"font-semibold text-white text-lg mb-3\">{feature.title}</h3>\n                <p className=\"text-white/70\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        );\n\n      case \"business\":\n        return (\n          <div className=\"space-y-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-accent mb-2\">\n                ${content.totalMRR.toLocaleString()} MRR\n              </div>\n              <div className=\"text-white/70\">Projected Monthly Recurring Revenue</div>\n            </div>\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              {content.tiers.map((tier, index) => (\n                <div key={index} className=\"text-center p-4 bg-white/5 border border-white/10 rounded-lg\">\n                  <div className=\"font-semibold text-white mb-2\">{tier.name}</div>\n                  <div className=\"text-2xl font-bold text-primary mb-1\">${tier.price}</div>\n                  <div className=\"text-white/70 text-sm\">{tier.users} users</div>\n                  <div className=\"text-accent text-sm\">${tier.revenue.toLocaleString()}/mo</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        );\n\n      case \"traction\":\n        return (\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6\">\n            {content.metrics.map((metric, index) => (\n              <div key={index} className=\"text-center p-6 bg-white/5 border border-white/10 rounded-lg\">\n                <div className=\"text-3xl font-bold text-primary mb-2\">{metric.value}</div>\n                <div className=\"text-white/70 mb-2\">{metric.label}</div>\n                <div className=\"text-accent text-sm\">{metric.growth}</div>\n              </div>\n            ))}\n          </div>\n        );\n\n      case \"funding\":\n        return (\n          <div className=\"space-y-8\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl font-bold text-primary mb-4\">\n                ${(content.ask / 1000000).toFixed(1)}M\n              </div>\n              <div className=\"text-xl text-white/80\">Series A Funding</div>\n            </div>\n            <div className=\"grid lg:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"font-semibold text-white mb-4\">Fund Allocation</h3>\n                <div className=\"space-y-3\">\n                  {content.allocation.map((item, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-white/5 rounded-lg\">\n                      <span className=\"text-white\">{item.category}</span>\n                      <div className=\"text-right\">\n                        <div className=\"text-primary font-semibold\">{item.percentage}%</div>\n                        <div className=\"text-white/70 text-sm\">${(item.amount / 1000000).toFixed(1)}M</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-white mb-4\">Key Milestones</h3>\n                <div className=\"space-y-3\">\n                  {content.milestones.map((milestone, index) => (\n                    <div key={index} className=\"flex items-center space-x-3 p-3 bg-accent/10 border border-accent/20 rounded-lg\">\n                      <div className=\"w-2 h-2 bg-accent rounded-full\"></div>\n                      <span className=\"text-white\">{milestone}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div className=\"text-white\">Content not available</div>;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-space via-surface to-space\">\n      <Navigation />\n      \n      {/* Pitch Header */}\n      <section className=\"pt-24 pb-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"font-heading text-4xl md:text-6xl font-bold mb-4\">\n              <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n                WarrantyAI\n              </span>\n            </h1>\n            <p className=\"text-xl text-white/80\">Investor Pitch Deck</p>\n          </div>\n\n          {/* Slide Controls */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={prevSlide}\n                className=\"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-all duration-300\"\n              >\n                <ChevronLeft className=\"w-5 h-5 text-white\" />\n              </button>\n              <button\n                onClick={nextSlide}\n                className=\"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-all duration-300\"\n              >\n                <ChevronRight className=\"w-5 h-5 text-white\" />\n              </button>\n              <button\n                onClick={() => setIsAutoPlay(!isAutoPlay)}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-primary/20 hover:bg-primary/30 border border-primary/30 rounded-lg transition-all duration-300\"\n              >\n                {isAutoPlay ? <Pause className=\"w-4 h-4\" /> : <Play className=\"w-4 h-4\" />}\n                <span className=\"text-primary\">{isAutoPlay ? 'Pause' : 'Auto Play'}</span>\n              </button>\n            </div>\n\n            <div className=\"text-white/60\">\n              {currentSlide + 1} / {slides.length}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Main Slide */}\n      <section className=\"pb-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div ref={slideRef} className=\"glass rounded-lg border border-white/10 overflow-hidden\">\n            \n            {/* Slide Header */}\n            <div className=\"bg-surface/50 border-b border-white/10 p-8 text-center\">\n              <h2 className=\"font-heading text-3xl md:text-4xl font-bold text-white mb-2\">\n                {currentSlideData.title}\n              </h2>\n              <p className=\"text-xl text-white/80\">{currentSlideData.subtitle}</p>\n            </div>\n\n            {/* Slide Content */}\n            <div className=\"p-8\">\n              {renderSlideContent()}\n            </div>\n          </div>\n\n          {/* Slide Navigation Dots */}\n          <div className=\"flex items-center justify-center space-x-2 mt-8\">\n            {slides.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentSlide\n                    ? 'bg-primary scale-125'\n                    : 'bg-white/20 hover:bg-white/40'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,OAAO;oBACL;wBAAE,OAAO;wBAAS,OAAO;wBAAiC,OAAO;oBAAU;oBAC3E;wBAAE,OAAO;wBAAO,OAAO;wBAAwB,OAAO;oBAAU;oBAChE;wBAAE,OAAO;wBAAO,OAAO;wBAAuB,OAAO;oBAAa;oBAClE;wBAAE,OAAO;wBAAK,OAAO;wBAAqB,OAAO;oBAAU;iBAC5D;gBACD,YAAY;oBACV;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,YAAY;oBACV,SAAS;oBACT,WAAW;oBACX,QAAQ;gBACV;gBACA,UAAU;oBACR;wBAAE,MAAM;wBAAwB,OAAO;wBAAI,OAAO;oBAAU;oBAC5D;wBAAE,MAAM;wBAAmB,OAAO;wBAAI,OAAO;oBAAY;oBACzD;wBAAE,MAAM;wBAAc,OAAO;wBAAI,OAAO;oBAAS;oBACjD;wBAAE,MAAM;wBAAS,OAAO;wBAAI,OAAO;oBAAQ;iBAC5C;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,UAAU;oBACR;wBACE,oBAAM,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBACrB,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACxB,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACvB,OAAO;wBACP,aAAa;oBACf;iBACD;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,OAAO;oBACL;wBAAE,MAAM;wBAAQ,OAAO;wBAAG,OAAO;wBAAS,SAAS;oBAAE;oBACrD;wBAAE,MAAM;wBAAO,OAAO;wBAAM,OAAO;wBAAQ,SAAS;oBAAM;oBAC1D;wBAAE,MAAM;wBAAW,OAAO;wBAAO,OAAO;wBAAO,SAAS;oBAAM;oBAC9D;wBAAE,MAAM;wBAAY,OAAO;wBAAI,OAAO;wBAAO,SAAS;oBAAK;iBAC5D;gBACD,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,aAAa;oBACX;wBAAE,MAAM;wBAAc,IAAI;wBAAM,WAAW;wBAAM,IAAI;wBAAM,QAAQ;wBAAM,OAAO;oBAAG;oBACnF;wBAAE,MAAM;wBAAQ,IAAI;wBAAO,WAAW;wBAAO,IAAI;wBAAO,QAAQ;wBAAO,OAAO;oBAAG;oBACjF;wBAAE,MAAM;wBAAa,IAAI;wBAAO,WAAW;wBAAM,IAAI;wBAAO,QAAQ;wBAAO,OAAO;oBAAG;oBACrF;wBAAE,MAAM;wBAAU,IAAI;wBAAO,WAAW;wBAAO,IAAI;wBAAO,QAAQ;wBAAO,OAAO;oBAAG;iBACpF;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP;wBAAE,OAAO;wBAAc,OAAO;wBAAU,QAAQ;oBAAQ;oBACxD;wBAAE,OAAO;wBAAiB,OAAO;wBAAW,QAAQ;oBAAQ;oBAC5D;wBAAE,OAAO;wBAAiB,OAAO;wBAAS,QAAQ;oBAAQ;oBAC1D;wBAAE,OAAO;wBAAkB,OAAO;wBAAO,QAAQ;oBAAO;iBACzD;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,aAAa;oBACX;wBAAE,MAAM;wBAAQ,OAAO;wBAAO,SAAS;wBAAQ,UAAU;oBAAO;oBAChE;wBAAE,MAAM;wBAAQ,OAAO;wBAAQ,SAAS;wBAAQ,UAAU;oBAAO;oBACjE;wBAAE,MAAM;wBAAQ,OAAO;wBAAQ,SAAS;wBAAM,UAAU;oBAAK;oBAC7D;wBAAE,MAAM;wBAAQ,OAAO;wBAAM,SAAS;wBAAO,UAAU;oBAAM;iBAC9D;YACH;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,KAAK;gBACL,YAAY;oBACV;wBAAE,UAAU;wBAAuB,YAAY;wBAAI,QAAQ;oBAAQ;oBACnE;wBAAE,UAAU;wBAAqB,YAAY;wBAAI,QAAQ;oBAAQ;oBACjE;wBAAE,UAAU;wBAAkB,YAAY;wBAAI,QAAQ;oBAAQ;oBAC9D;wBAAE,UAAU;wBAAc,YAAY;wBAAI,QAAQ;oBAAO;iBAC1D;gBACD,YAAY;oBACV;oBACA;oBACA;oBACA;iBACD;YACH;QACF;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,OAAO,EAAE;gBACpB,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SAAS,OAAO,EAC1B;oBAAE,SAAS;oBAAG,GAAG;gBAAG,GACpB;oBAAE,SAAS;oBAAG,GAAG;oBAAG,UAAU;oBAAK,MAAM;gBAAa;YAE1D;QACF;8BAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI;YACJ,IAAI,YAAY;gBACd,WAAW;2CAAY;wBACrB;mDAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;oBACpD;0CAAG;YACL;YACA;uCAAO,IAAM,cAAc;;QAC7B;8BAAG;QAAC;QAAY,OAAO,MAAM;KAAC;IAE9B,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IACtE;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,MAAM,CAAC,aAAa;IAE7C,MAAM,qBAAqB;QACzB,MAAM,EAAE,OAAO,EAAE,GAAG;QAEpB,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;oCAAgB,WAAW,CAAC,yCAAyC,EAAE,KAAK,KAAK,CAAC,cAAc,CAAC;;sDAChG,6LAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC;sDAAG,KAAK,KAAK;;;;;;sDACzE,6LAAC;4CAAI,WAAU;sDAAyB,KAAK,KAAK;;;;;;;mCAF1C;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;mCAFtB;;;;;;;;;;;;;;;;YASpB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAuC;wCAAE,QAAQ,UAAU,CAAC,SAAS;wCAAC;;;;;;;8CACrF,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CAAe,QAAQ,UAAU,CAAC,MAAM;;;;;;;;;;;;sCAEzD,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;oCAAgB,WAAW,CAAC,mBAAmB,EAAE,QAAQ,KAAK,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,cAAc,CAAC;;sDAC/G,6LAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;gDAAG,QAAQ,KAAK;gDAAC;;;;;;;sDAChF,6LAAC;4CAAI,WAAU;sDAAyB,QAAQ,IAAI;;;;;;;mCAF5C;;;;;;;;;;;;;;;;YASpB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CAAqB,QAAQ,IAAI;;;;;;8CAChD,6LAAC;oCAAG,WAAU;8CAAyC,QAAQ,KAAK;;;;;;8CACpE,6LAAC;oCAAE,WAAU;8CAAiB,QAAQ,WAAW;;;;;;;2BAHzC;;;;;;;;;;YASlB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAsC;wCACjD,QAAQ,QAAQ,CAAC,cAAc;wCAAG;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDAAiC,KAAK,IAAI;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;gDAAuC;gDAAE,KAAK,KAAK;;;;;;;sDAClE,6LAAC;4CAAI,WAAU;;gDAAyB,KAAK,KAAK;gDAAC;;;;;;;sDACnD,6LAAC;4CAAI,WAAU;;gDAAsB;gDAAE,KAAK,OAAO,CAAC,cAAc;gDAAG;;;;;;;;mCAJ7D;;;;;;;;;;;;;;;;YAWpB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CAAwC,OAAO,KAAK;;;;;;8CACnE,6LAAC;oCAAI,WAAU;8CAAsB,OAAO,KAAK;;;;;;8CACjD,6LAAC;oCAAI,WAAU;8CAAuB,OAAO,MAAM;;;;;;;2BAH3C;;;;;;;;;;YASlB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAuC;wCAClD,CAAC,QAAQ,GAAG,GAAG,OAAO,EAAE,OAAO,CAAC;wCAAG;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAK,WAAU;sEAAc,KAAK,QAAQ;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAA8B,KAAK,UAAU;wEAAC;;;;;;;8EAC7D,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,CAAC,KAAK,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;mDAJtE;;;;;;;;;;;;;;;;8CAUhB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAClC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;mDAFtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWxB;gBACE,qBAAO,6LAAC;oBAAI,WAAU;8BAAa;;;;;;QACvC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;8CAIxG,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CACC,SAAS,IAAM,cAAc,CAAC;4CAC9B,WAAU;;gDAET,2BAAa,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAAe,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAC9D,6LAAC;oDAAK,WAAU;8DAAgB,aAAa,UAAU;;;;;;;;;;;;;;;;;;8CAI3D,6LAAC;oCAAI,WAAU;;wCACZ,eAAe;wCAAE;wCAAI,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,KAAK;4BAAU,WAAU;;8CAG5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,iBAAiB,KAAK;;;;;;sDAEzB,6LAAC;4CAAE,WAAU;sDAAyB,iBAAiB,QAAQ;;;;;;;;;;;;8CAIjE,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,yBACA,iCACJ;mCANG;;;;;;;;;;;;;;;;;;;;;0BAaf,6LAAC,oIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAtZwB;KAAA", "debugId": null}}]}