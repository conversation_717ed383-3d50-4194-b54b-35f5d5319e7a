{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,6LAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KApNM;uCAsNS", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/DemoLevelSelector.js"], "sourcesContent": ["'use client';\n\nimport { Check, Lock, Play } from 'lucide-react';\n\nconst DemoLevelSelector = ({ levels, currentLevel, completedLevels, onLevelSelect }) => {\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'Beginner': return 'accent';\n      case 'Intermediate': return 'primary';\n      case 'Advanced': return 'secondary';\n      case 'Expert': return 'white';\n      default: return 'white';\n    }\n  };\n\n  const isLevelUnlocked = (levelId) => {\n    if (levelId === 1) return true;\n    return completedLevels.includes(levelId - 1);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"glass rounded-lg p-4 border border-white/10\">\n        <h3 className=\"font-heading text-lg font-bold text-white mb-4\">\n          Demo Levels\n        </h3>\n        \n        <div className=\"space-y-3\">\n          {levels.map((level) => {\n            const isCompleted = completedLevels.includes(level.id);\n            const isCurrent = currentLevel === level.id;\n            const isUnlocked = isLevelUnlocked(level.id);\n            const difficultyColor = getDifficultyColor(level.difficulty);\n\n            return (\n              <button\n                key={level.id}\n                onClick={() => isUnlocked && onLevelSelect(level.id)}\n                disabled={!isUnlocked}\n                className={`w-full text-left p-4 rounded-lg border transition-all duration-300 ${\n                  isCurrent\n                    ? 'border-primary bg-primary/10 glow-primary'\n                    : isCompleted\n                    ? 'border-accent/30 bg-accent/5 hover:border-accent/50'\n                    : isUnlocked\n                    ? 'border-white/20 hover:border-white/40 hover:bg-white/5'\n                    : 'border-white/10 bg-white/5 opacity-50 cursor-not-allowed'\n                }`}\n              >\n                <div className=\"flex items-start space-x-3\">\n                  {/* Status Icon */}\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mt-1 ${\n                    isCompleted\n                      ? 'bg-accent text-space'\n                      : isCurrent\n                      ? 'bg-primary text-space'\n                      : isUnlocked\n                      ? 'bg-white/10 text-white/60'\n                      : 'bg-white/5 text-white/30'\n                  }`}>\n                    {isCompleted ? (\n                      <Check className=\"w-4 h-4\" />\n                    ) : isCurrent ? (\n                      <Play className=\"w-4 h-4\" />\n                    ) : isUnlocked ? (\n                      <span className=\"text-xs font-bold\">{level.id}</span>\n                    ) : (\n                      <Lock className=\"w-4 h-4\" />\n                    )}\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <h4 className={`font-semibold text-sm ${\n                        isCurrent ? 'text-white' : isUnlocked ? 'text-white/90' : 'text-white/50'\n                      }`}>\n                        {level.title}\n                      </h4>\n                      <span className={`text-xs px-2 py-1 rounded-full border ${\n                        isUnlocked \n                          ? `border-${difficultyColor}/30 text-${difficultyColor}`\n                          : 'border-white/20 text-white/30'\n                      }`}>\n                        {level.difficulty}\n                      </span>\n                    </div>\n                    <p className={`text-xs leading-relaxed ${\n                      isUnlocked ? 'text-white/70' : 'text-white/40'\n                    }`}>\n                      {level.description}\n                    </p>\n                    <div className={`text-xs mt-2 ${\n                      isUnlocked ? 'text-white/60' : 'text-white/30'\n                    }`}>\n                      ⏱️ {level.duration}\n                    </div>\n                  </div>\n                </div>\n              </button>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Progress Summary */}\n      <div className=\"glass rounded-lg p-4 border border-white/10\">\n        <h4 className=\"font-heading text-sm font-bold text-white mb-3\">\n          Your Progress\n        </h4>\n        \n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-white/70\">Completed</span>\n            <span className=\"text-sm font-semibold text-accent\">\n              {completedLevels.length} / {levels.length}\n            </span>\n          </div>\n          \n          <div className=\"w-full bg-surface/50 rounded-full h-2\">\n            <div \n              className=\"bg-gradient-to-r from-accent to-primary h-2 rounded-full transition-all duration-500\"\n              style={{ width: `${(completedLevels.length / levels.length) * 100}%` }}\n            ></div>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-3 text-xs\">\n            <div className=\"text-center p-2 bg-white/5 rounded\">\n              <div className=\"font-semibold text-white\">\n                {Math.round((completedLevels.length / levels.length) * 100)}%\n              </div>\n              <div className=\"text-white/60\">Complete</div>\n            </div>\n            <div className=\"text-center p-2 bg-white/5 rounded\">\n              <div className=\"font-semibold text-white\">\n                {levels.length - completedLevels.length}\n              </div>\n              <div className=\"text-white/60\">Remaining</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"glass rounded-lg p-4 border border-white/10\">\n        <h4 className=\"font-heading text-sm font-bold text-white mb-3\">\n          Quick Actions\n        </h4>\n        \n        <div className=\"space-y-2\">\n          <button className=\"w-full text-left px-3 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 rounded transition-all duration-300\">\n            🔄 Reset Progress\n          </button>\n          <button className=\"w-full text-left px-3 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 rounded transition-all duration-300\">\n            📊 View Analytics\n          </button>\n          <button className=\"w-full text-left px-3 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 rounded transition-all duration-300\">\n            💾 Save Progress\n          </button>\n          <button className=\"w-full text-left px-3 py-2 text-sm text-white/70 hover:text-white hover:bg-white/5 rounded transition-all duration-300\">\n            🎯 Skip to Level\n          </button>\n        </div>\n      </div>\n\n      {/* Achievement Badges */}\n      {completedLevels.length > 0 && (\n        <div className=\"glass rounded-lg p-4 border border-white/10\">\n          <h4 className=\"font-heading text-sm font-bold text-white mb-3\">\n            Achievements\n          </h4>\n          \n          <div className=\"grid grid-cols-2 gap-2\">\n            {completedLevels.length >= 1 && (\n              <div className=\"text-center p-2 bg-accent/10 border border-accent/20 rounded\">\n                <div className=\"text-lg\">🚀</div>\n                <div className=\"text-xs text-accent\">First Steps</div>\n              </div>\n            )}\n            {completedLevels.length >= 3 && (\n              <div className=\"text-center p-2 bg-primary/10 border border-primary/20 rounded\">\n                <div className=\"text-lg\">⚡</div>\n                <div className=\"text-xs text-primary\">Quick Learner</div>\n              </div>\n            )}\n            {completedLevels.length >= 5 && (\n              <div className=\"text-center p-2 bg-secondary/10 border border-secondary/20 rounded\">\n                <div className=\"text-lg\">🎯</div>\n                <div className=\"text-xs text-secondary\">Expert</div>\n              </div>\n            )}\n            {completedLevels.length === levels.length && (\n              <div className=\"text-center p-2 bg-white/10 border border-white/20 rounded\">\n                <div className=\"text-lg\">👑</div>\n                <div className=\"text-xs text-white\">Master</div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DemoLevelSelector;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;;AAIA,MAAM,oBAAoB,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE;IACjF,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY,GAAG,OAAO;QAC1B,OAAO,gBAAgB,QAAQ,CAAC,UAAU;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC;4BACX,MAAM,cAAc,gBAAgB,QAAQ,CAAC,MAAM,EAAE;4BACrD,MAAM,YAAY,iBAAiB,MAAM,EAAE;4BAC3C,MAAM,aAAa,gBAAgB,MAAM,EAAE;4BAC3C,MAAM,kBAAkB,mBAAmB,MAAM,UAAU;4BAE3D,qBACE,6LAAC;gCAEC,SAAS,IAAM,cAAc,cAAc,MAAM,EAAE;gCACnD,UAAU,CAAC;gCACX,WAAW,CAAC,mEAAmE,EAC7E,YACI,8CACA,cACA,wDACA,aACA,2DACA,4DACJ;0CAEF,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAW,CAAC,2DAA2D,EAC1E,cACI,yBACA,YACA,0BACA,aACA,8BACA,4BACJ;sDACC,4BACC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uDACf,0BACF,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;uDACd,2BACF,6LAAC;gDAAK,WAAU;0DAAqB,MAAM,EAAE;;;;;qEAE7C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAKpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,CAAC,sBAAsB,EACpC,YAAY,eAAe,aAAa,kBAAkB,iBAC1D;sEACC,MAAM,KAAK;;;;;;sEAEd,6LAAC;4DAAK,WAAW,CAAC,sCAAsC,EACtD,aACI,CAAC,OAAO,EAAE,gBAAgB,SAAS,EAAE,iBAAiB,GACtD,iCACJ;sEACC,MAAM,UAAU;;;;;;;;;;;;8DAGrB,6LAAC;oDAAE,WAAW,CAAC,wBAAwB,EACrC,aAAa,kBAAkB,iBAC/B;8DACC,MAAM,WAAW;;;;;;8DAEpB,6LAAC;oDAAI,WAAW,CAAC,aAAa,EAC5B,aAAa,kBAAkB,iBAC/B;;wDAAE;wDACE,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;+BA3DnB,MAAM,EAAE;;;;;wBAiEnB;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CACb,gBAAgB,MAAM;4CAAC;4CAAI,OAAO,MAAM;;;;;;;;;;;;;0CAI7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,AAAC,gBAAgB,MAAM,GAAG,OAAO,MAAM,GAAI,IAAI,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAIzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,AAAC,gBAAgB,MAAM,GAAG,OAAO,MAAM,GAAI;oDAAK;;;;;;;0DAE9D,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,GAAG,gBAAgB,MAAM;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAyH;;;;;;0CAG3I,6LAAC;gCAAO,WAAU;0CAAyH;;;;;;0CAG3I,6LAAC;gCAAO,WAAU;0CAAyH;;;;;;0CAG3I,6LAAC;gCAAO,WAAU;0CAAyH;;;;;;;;;;;;;;;;;;YAO9I,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,MAAM,IAAI,mBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAU;;;;;;kDACzB,6LAAC;wCAAI,WAAU;kDAAsB;;;;;;;;;;;;4BAGxC,gBAAgB,MAAM,IAAI,mBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAU;;;;;;kDACzB,6LAAC;wCAAI,WAAU;kDAAuB;;;;;;;;;;;;4BAGzC,gBAAgB,MAAM,IAAI,mBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAU;;;;;;kDACzB,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;4BAG3C,gBAAgB,MAAM,KAAK,OAAO,MAAM,kBACvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAU;;;;;;kDACzB,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;KAtMM;uCAwMS", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/ReceiptUploadDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\nimport { Upload, Camera, FileText, Check, X, Eye, Brain } from 'lucide-react';\n\nconst ReceiptUploadDemo = ({ onComplete, isActive }) => {\n  const [uploadState, setUploadState] = useState('idle'); // idle, uploading, processing, completed\n  const [dragActive, setDragActive] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const fileInputRef = useRef(null);\n\n  // Sample receipt images for demo\n  const sampleReceipts = [\n    { id: 1, name: 'iPhone Receipt', url: '/demo/receipt-iphone.jpg', type: 'Electronics' },\n    { id: 2, name: 'Fridge Receipt', url: '/demo/receipt-fridge.jpg', type: 'Appliance' },\n    { id: 3, name: 'Laptop Receipt', url: '/demo/receipt-laptop.jpg', type: 'Electronics' }\n  ];\n\n  const simulateUpload = useCallback(async (file) => {\n    setUploadState('uploading');\n    setUploadProgress(0);\n\n    // Simulate upload progress\n    for (let i = 0; i <= 100; i += 10) {\n      await new Promise(resolve => setTimeout(resolve, 100));\n      setUploadProgress(i);\n    }\n\n    setUploadState('processing');\n    \n    // Simulate processing\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setUploadState('completed');\n    \n    // Auto-complete after showing success\n    setTimeout(() => {\n      onComplete && onComplete();\n    }, 1500);\n  }, [onComplete]);\n\n  const handleDrag = useCallback((e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\n      setDragActive(true);\n    } else if (e.type === \"dragleave\") {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDrop = useCallback((e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0];\n      handleFileSelect(file);\n    }\n  }, []);\n\n  const handleFileSelect = (file) => {\n    if (file && file.type.startsWith('image/')) {\n      setSelectedFile(file);\n      const url = URL.createObjectURL(file);\n      setPreviewUrl(url);\n      simulateUpload(file);\n    }\n  };\n\n  const handleFileInput = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      handleFileSelect(e.target.files[0]);\n    }\n  };\n\n  const handleSampleSelect = (sample) => {\n    setSelectedFile({ name: sample.name, type: sample.type });\n    setPreviewUrl(sample.url);\n    simulateUpload({ name: sample.name });\n  };\n\n  const resetDemo = () => {\n    setUploadState('idle');\n    setUploadProgress(0);\n    setSelectedFile(null);\n    setPreviewUrl(null);\n    setDragActive(false);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Demo Instructions */}\n      <div className=\"bg-primary/10 border border-primary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-primary mb-2\">📋 Demo Instructions</h3>\n        <p className=\"text-white/80 text-sm\">\n          Upload a receipt image or select a sample to see our AI-powered scanning in action. \n          The system will automatically detect and extract warranty information.\n        </p>\n      </div>\n\n      {/* Upload Area */}\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        \n        {/* File Upload Zone */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Upload Receipt</h4>\n          \n          <div\n            className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${\n              dragActive\n                ? 'border-primary bg-primary/10'\n                : uploadState === 'idle'\n                ? 'border-white/30 hover:border-primary/50 hover:bg-primary/5'\n                : 'border-accent bg-accent/10'\n            }`}\n            onDragEnter={handleDrag}\n            onDragLeave={handleDrag}\n            onDragOver={handleDrag}\n            onDrop={handleDrop}\n          >\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileInput}\n              className=\"hidden\"\n            />\n\n            {uploadState === 'idle' && (\n              <>\n                <Upload className=\"w-12 h-12 text-white/60 mx-auto mb-4\" />\n                <p className=\"text-white/80 mb-2\">Drag & drop your receipt here</p>\n                <p className=\"text-white/60 text-sm mb-4\">or</p>\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"bg-primary/20 hover:bg-primary/30 border border-primary/30 px-6 py-2 rounded-lg text-primary transition-all duration-300\"\n                >\n                  Browse Files\n                </button>\n              </>\n            )}\n\n            {uploadState === 'uploading' && (\n              <>\n                <div className=\"w-12 h-12 border-4 border-primary/30 border-t-primary rounded-full animate-spin mx-auto mb-4\"></div>\n                <p className=\"text-white/80 mb-2\">Uploading...</p>\n                <div className=\"w-full bg-white/20 rounded-full h-2 mb-2\">\n                  <div \n                    className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${uploadProgress}%` }}\n                  ></div>\n                </div>\n                <p className=\"text-white/60 text-sm\">{uploadProgress}% complete</p>\n              </>\n            )}\n\n            {uploadState === 'processing' && (\n              <>\n                <Brain className=\"w-12 h-12 text-secondary mx-auto mb-4 animate-pulse\" />\n                <p className=\"text-white/80 mb-2\">AI Processing...</p>\n                <p className=\"text-white/60 text-sm\">Extracting warranty information</p>\n              </>\n            )}\n\n            {uploadState === 'completed' && (\n              <>\n                <Check className=\"w-12 h-12 text-accent mx-auto mb-4\" />\n                <p className=\"text-accent mb-2\">Upload Successful!</p>\n                <p className=\"text-white/60 text-sm\">Receipt processed and analyzed</p>\n              </>\n            )}\n          </div>\n\n          {/* Sample Receipts */}\n          <div>\n            <h5 className=\"font-semibold text-white mb-3\">Try Sample Receipts</h5>\n            <div className=\"grid grid-cols-1 gap-2\">\n              {sampleReceipts.map((sample) => (\n                <button\n                  key={sample.id}\n                  onClick={() => handleSampleSelect(sample)}\n                  disabled={uploadState !== 'idle'}\n                  className=\"flex items-center space-x-3 p-3 border border-white/20 rounded-lg hover:border-primary/50 hover:bg-primary/5 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <FileText className=\"w-5 h-5 text-primary\" />\n                  <div className=\"text-left\">\n                    <div className=\"text-white text-sm font-medium\">{sample.name}</div>\n                    <div className=\"text-white/60 text-xs\">{sample.type}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Preview Area */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Preview</h4>\n          \n          <div className=\"border border-white/20 rounded-lg p-4 h-80 flex items-center justify-center bg-white/5\">\n            {previewUrl ? (\n              <div className=\"relative w-full h-full\">\n                <img\n                  src={previewUrl}\n                  alt=\"Receipt preview\"\n                  className=\"w-full h-full object-contain rounded\"\n                />\n                <button className=\"absolute top-2 right-2 w-8 h-8 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-all duration-300\">\n                  <Eye className=\"w-4 h-4\" />\n                </button>\n              </div>\n            ) : (\n              <div className=\"text-center\">\n                <Camera className=\"w-12 h-12 text-white/40 mx-auto mb-2\" />\n                <p className=\"text-white/60\">No image selected</p>\n              </div>\n            )}\n          </div>\n\n          {selectedFile && (\n            <div className=\"space-y-2\">\n              <h5 className=\"font-semibold text-white text-sm\">File Details</h5>\n              <div className=\"bg-white/5 rounded-lg p-3 space-y-1\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-white/60\">Name:</span>\n                  <span className=\"text-white\">{selectedFile.name}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-white/60\">Type:</span>\n                  <span className=\"text-white\">{selectedFile.type || 'Sample Image'}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-white/60\">Status:</span>\n                  <span className={`${\n                    uploadState === 'completed' ? 'text-accent' : \n                    uploadState === 'processing' ? 'text-secondary' :\n                    uploadState === 'uploading' ? 'text-primary' : 'text-white'\n                  }`}>\n                    {uploadState === 'completed' ? 'Processed' : \n                     uploadState === 'processing' ? 'Processing' :\n                     uploadState === 'uploading' ? 'Uploading' : 'Ready'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button\n          onClick={resetDemo}\n          className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n        >\n          Reset Demo\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          {uploadState === 'completed' && (\n            <div className=\"flex items-center space-x-2 text-accent\">\n              <Check className=\"w-4 h-4\" />\n              <span className=\"text-sm\">Level Complete!</span>\n            </div>\n          )}\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            disabled={uploadState !== 'completed'}\n            className=\"px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:shadow-primary/25 transition-all duration-300\"\n          >\n            Continue to Next Level\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReceiptUploadDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,yCAAyC;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,iCAAiC;IACjC,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAG,MAAM;YAAkB,KAAK;YAA4B,MAAM;QAAc;QACtF;YAAE,IAAI;YAAG,MAAM;YAAkB,KAAK;YAA4B,MAAM;QAAY;QACpF;YAAE,IAAI;YAAG,MAAM;YAAkB,KAAK;YAA4B,MAAM;QAAc;KACvF;IAED,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YACxC,eAAe;YACf,kBAAkB;YAElB,2BAA2B;YAC3B,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;gBACjC,MAAM,IAAI;qEAAQ,CAAA,UAAW,WAAW,SAAS;;gBACjD,kBAAkB;YACpB;YAEA,eAAe;YAEf,sBAAsB;YACtB,MAAM,IAAI;iEAAQ,CAAA,UAAW,WAAW,SAAS;;YAEjD,eAAe;YAEf,sCAAsC;YACtC;iEAAW;oBACT,cAAc;gBAChB;gEAAG;QACL;wDAAG;QAAC;KAAW;IAEf,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;gBACnD,cAAc;YAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;gBACjC,cAAc;YAChB;QACF;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;gBACnD,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;gBACpC,iBAAiB;YACnB;QACF;oDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,gBAAgB;YAChB,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;YACd,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,iBAAiB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QACpC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YAAE,MAAM,OAAO,IAAI;YAAE,MAAM,OAAO,IAAI;QAAC;QACvD,cAAc,OAAO,GAAG;QACxB,eAAe;YAAE,MAAM,OAAO,IAAI;QAAC;IACrC;IAEA,MAAM,YAAY;QAChB,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAChB,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCACC,WAAW,CAAC,uFAAuF,EACjG,aACI,iCACA,gBAAgB,SAChB,+DACA,8BACJ;gCACF,aAAa;gCACb,aAAa;gCACb,YAAY;gCACZ,QAAQ;;kDAER,6LAAC;wCACC,KAAK;wCACL,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;oCAGX,gBAAgB,wBACf;;0DACE,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDACC,SAAS,IAAM,aAAa,OAAO,EAAE;gDACrC,WAAU;0DACX;;;;;;;;oCAMJ,gBAAgB,6BACf;;0DACE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGzC,6LAAC;gDAAE,WAAU;;oDAAyB;oDAAe;;;;;;;;;oCAIxD,gBAAgB,8BACf;;0DACE,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;oCAIxC,gBAAgB,6BACf;;0DACE,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;0CAM3C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,UAAU,gBAAgB;gDAC1B,WAAU;;kEAEV,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAkC,OAAO,IAAI;;;;;;0EAC5D,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,IAAI;;;;;;;;;;;;;+CARhD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAiBxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;yDAInB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;4BAKlC,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAc,aAAa,IAAI;;;;;;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAc,aAAa,IAAI,IAAI;;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAW,GACf,gBAAgB,cAAc,gBAC9B,gBAAgB,eAAe,mBAC/B,gBAAgB,cAAc,iBAAiB,cAC/C;kEACC,gBAAgB,cAAc,cAC9B,gBAAgB,eAAe,eAC/B,gBAAgB,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,6BACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GApRM;KAAA;uCAsRS", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/data/demoData.js"], "sourcesContent": ["// Demo data for WarrantyAI interactive demonstrations\n\nexport const demoItems = [\n  {\n    id: 1,\n    name: \"iPhone 15 Pro\",\n    brand: \"Apple\",\n    model: \"A3108\",\n    category: \"Electronics\",\n    purchaseDate: \"2024-01-15\",\n    warrantyExpiry: \"2025-01-15\",\n    warrantyType: \"Limited Warranty\",\n    warrantyDuration: \"1 year\",\n    price: 999.99,\n    serialNumber: \"F2LW48XHQM\",\n    retailer: \"Apple Store\",\n    status: \"Active\",\n    daysRemaining: 45,\n    image: \"/demo/iphone.jpg\",\n    receipt: \"/demo/receipt-iphone.jpg\",\n    documents: [\n      { name: \"Purchase Receipt\", type: \"receipt\", url: \"/demo/receipt-iphone.jpg\" },\n      { name: \"Warranty Card\", type: \"warranty\", url: \"/demo/warranty-iphone.pdf\" }\n    ],\n    reminders: [\n      { type: \"warranty_expiry\", date: \"2024-12-15\", message: \"Warranty expires in 30 days\" },\n      { type: \"backup_reminder\", date: \"2024-12-01\", message: \"Create device backup\" }\n    ]\n  },\n  {\n    id: 2,\n    name: \"Samsung Refrigerator\",\n    brand: \"Samsung\",\n    model: \"RF28T5001SR\",\n    category: \"Home Appliances\",\n    purchaseDate: \"2023-06-20\",\n    warrantyExpiry: \"2025-06-20\",\n    warrantyType: \"Manufacturer Warranty\",\n    warrantyDuration: \"2 years\",\n    price: 1299.99,\n    serialNumber: \"SN123456789\",\n    retailer: \"Home Depot\",\n    status: \"Active\",\n    daysRemaining: 180,\n    image: \"/demo/fridge.jpg\",\n    receipt: \"/demo/receipt-fridge.jpg\",\n    documents: [\n      { name: \"Purchase Receipt\", type: \"receipt\", url: \"/demo/receipt-fridge.jpg\" },\n      { name: \"Installation Guide\", type: \"manual\", url: \"/demo/manual-fridge.pdf\" }\n    ],\n    reminders: [\n      { type: \"filter_change\", date: \"2024-12-30\", message: \"Replace water filter\" },\n      { type: \"maintenance\", date: \"2025-01-15\", message: \"Schedule maintenance check\" }\n    ]\n  },\n  {\n    id: 3,\n    name: \"MacBook Pro 16\\\"\",\n    brand: \"Apple\",\n    model: \"M3 Max\",\n    category: \"Electronics\",\n    purchaseDate: \"2023-11-10\",\n    warrantyExpiry: \"2024-11-10\",\n    warrantyType: \"AppleCare+\",\n    warrantyDuration: \"1 year + AppleCare\",\n    price: 2499.99,\n    serialNumber: \"C02ZW0XHMD6T\",\n    retailer: \"Apple Store\",\n    status: \"Expired\",\n    daysRemaining: -30,\n    image: \"/demo/macbook.jpg\",\n    receipt: \"/demo/receipt-macbook.jpg\",\n    documents: [\n      { name: \"Purchase Receipt\", type: \"receipt\", url: \"/demo/receipt-macbook.jpg\" },\n      { name: \"AppleCare Agreement\", type: \"warranty\", url: \"/demo/applecare.pdf\" }\n    ],\n    reminders: [\n      { type: \"warranty_expired\", date: \"2024-11-10\", message: \"Warranty has expired\" },\n      { type: \"backup_reminder\", date: \"2024-12-01\", message: \"Create Time Machine backup\" }\n    ]\n  },\n  {\n    id: 4,\n    name: \"Tesla Model 3\",\n    brand: \"Tesla\",\n    model: \"Model 3 Long Range\",\n    category: \"Vehicle\",\n    purchaseDate: \"2023-03-15\",\n    warrantyExpiry: \"2027-03-15\",\n    warrantyType: \"Basic Vehicle Warranty\",\n    warrantyDuration: \"4 years / 50,000 miles\",\n    price: 47240.00,\n    serialNumber: \"5YJ3E1EA8PF123456\",\n    retailer: \"Tesla Service Center\",\n    status: \"Active\",\n    daysRemaining: 850,\n    image: \"/demo/tesla.jpg\",\n    receipt: \"/demo/receipt-tesla.jpg\",\n    documents: [\n      { name: \"Purchase Agreement\", type: \"receipt\", url: \"/demo/purchase-tesla.pdf\" },\n      { name: \"Vehicle Warranty\", type: \"warranty\", url: \"/demo/warranty-tesla.pdf\" }\n    ],\n    reminders: [\n      { type: \"service_due\", date: \"2024-12-20\", message: \"Annual service due\" },\n      { type: \"tire_rotation\", date: \"2024-12-15\", message: \"Tire rotation recommended\" }\n    ]\n  },\n  {\n    id: 5,\n    name: \"Dyson V15 Detect\",\n    brand: \"Dyson\",\n    model: \"V15 Detect\",\n    category: \"Home Appliances\",\n    purchaseDate: \"2024-02-28\",\n    warrantyExpiry: \"2026-02-28\",\n    warrantyType: \"Manufacturer Warranty\",\n    warrantyDuration: \"2 years\",\n    price: 749.99,\n    serialNumber: \"DY123456789\",\n    retailer: \"Best Buy\",\n    status: \"Active\",\n    daysRemaining: 430,\n    image: \"/demo/dyson.jpg\",\n    receipt: \"/demo/receipt-dyson.jpg\",\n    documents: [\n      { name: \"Purchase Receipt\", type: \"receipt\", url: \"/demo/receipt-dyson.jpg\" },\n      { name: \"User Manual\", type: \"manual\", url: \"/demo/manual-dyson.pdf\" }\n    ],\n    reminders: [\n      { type: \"filter_clean\", date: \"2024-12-25\", message: \"Clean HEPA filter\" },\n      { type: \"maintenance\", date: \"2025-02-28\", message: \"Annual maintenance check\" }\n    ]\n  }\n];\n\nexport const demoCategories = [\n  { id: 'all', name: 'All Items', count: demoItems.length },\n  { id: 'electronics', name: 'Electronics', count: demoItems.filter(item => item.category === 'Electronics').length },\n  { id: 'home', name: 'Home Appliances', count: demoItems.filter(item => item.category === 'Home Appliances').length },\n  { id: 'vehicle', name: 'Vehicle', count: demoItems.filter(item => item.category === 'Vehicle').length }\n];\n\nexport const demoStats = {\n  totalItems: demoItems.length,\n  activeWarranties: demoItems.filter(item => item.status === 'Active').length,\n  expiredWarranties: demoItems.filter(item => item.status === 'Expired').length,\n  expiringThisMonth: demoItems.filter(item => item.daysRemaining <= 30 && item.daysRemaining > 0).length,\n  totalValue: demoItems.reduce((sum, item) => sum + item.price, 0),\n  avgWarrantyDuration: 1.8 // years\n};\n\nexport const demoNotifications = [\n  {\n    id: 1,\n    type: 'warning',\n    title: 'Warranty Expiring Soon',\n    message: 'iPhone 15 Pro warranty expires in 45 days',\n    date: '2024-12-01',\n    read: false,\n    itemId: 1\n  },\n  {\n    id: 2,\n    type: 'info',\n    title: 'Service Reminder',\n    message: 'Tesla Model 3 annual service due in 20 days',\n    date: '2024-11-30',\n    read: false,\n    itemId: 4\n  },\n  {\n    id: 3,\n    type: 'error',\n    title: 'Warranty Expired',\n    message: 'MacBook Pro warranty expired 30 days ago',\n    date: '2024-11-10',\n    read: true,\n    itemId: 3\n  },\n  {\n    id: 4,\n    type: 'success',\n    title: 'Filter Replacement',\n    message: 'Samsung Refrigerator filter changed successfully',\n    date: '2024-11-25',\n    read: true,\n    itemId: 2\n  }\n];\n\nexport const demoUploadSimulation = {\n  steps: [\n    { id: 1, name: 'File Upload', status: 'completed', duration: 500 },\n    { id: 2, name: 'Image Processing', status: 'completed', duration: 1200 },\n    { id: 3, name: 'OCR Analysis', status: 'completed', duration: 800 },\n    { id: 4, name: 'AI Extraction', status: 'completed', duration: 1500 },\n    { id: 5, name: 'Data Validation', status: 'completed', duration: 600 },\n    { id: 6, name: 'Database Update', status: 'completed', duration: 400 }\n  ],\n  extractedData: {\n    retailer: 'Apple Store',\n    date: '2024-01-15',\n    total: '$999.99',\n    product: 'iPhone 15 Pro',\n    model: 'A3108',\n    serialNumber: 'F2LW48XHQM',\n    warranty: '1 Year Limited Warranty',\n    confidence: 98.5\n  }\n};\n\nexport const demo3DItems = [\n  {\n    id: 1,\n    name: \"iPhone 15 Pro\",\n    position: { x: 2, y: 1, z: 0 },\n    rotation: { x: 0, y: 0.5, z: 0 },\n    scale: { x: 1, y: 1, z: 1 },\n    color: \"#1f2937\",\n    room: \"office\"\n  },\n  {\n    id: 2,\n    name: \"Samsung Refrigerator\",\n    position: { x: -3, y: 0, z: -2 },\n    rotation: { x: 0, y: 0, z: 0 },\n    scale: { x: 1.5, y: 2, z: 1 },\n    color: \"#6b7280\",\n    room: \"kitchen\"\n  },\n  {\n    id: 3,\n    name: \"MacBook Pro\",\n    position: { x: 0, y: 1.2, z: 1 },\n    rotation: { x: -0.2, y: 0, z: 0 },\n    scale: { x: 1.2, y: 0.1, z: 0.8 },\n    color: \"#374151\",\n    room: \"office\"\n  },\n  {\n    id: 4,\n    name: \"Dyson V15\",\n    position: { x: -1, y: 0, z: 2 },\n    rotation: { x: 0, y: 0.3, z: 0 },\n    scale: { x: 0.3, y: 1.5, z: 0.3 },\n    color: \"#7c3aed\",\n    room: \"living\"\n  }\n];\n\nexport const demoRooms = [\n  { id: 'office', name: 'Office', items: 2, color: '#3b82f6' },\n  { id: 'kitchen', name: 'Kitchen', items: 1, color: '#10b981' },\n  { id: 'living', name: 'Living Room', items: 1, color: '#f59e0b' },\n  { id: 'bedroom', name: 'Bedroom', items: 0, color: '#8b5cf6' }\n];\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;;;;;AAE/C,MAAM,YAAY;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,OAAO;QACP,cAAc;QACd,UAAU;QACV,QAAQ;QACR,eAAe;QACf,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBAAE,MAAM;gBAAoB,MAAM;gBAAW,KAAK;YAA2B;YAC7E;gBAAE,MAAM;gBAAiB,MAAM;gBAAY,KAAK;YAA4B;SAC7E;QACD,WAAW;YACT;gBAAE,MAAM;gBAAmB,MAAM;gBAAc,SAAS;YAA8B;YACtF;gBAAE,MAAM;gBAAmB,MAAM;gBAAc,SAAS;YAAuB;SAChF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,OAAO;QACP,cAAc;QACd,UAAU;QACV,QAAQ;QACR,eAAe;QACf,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBAAE,MAAM;gBAAoB,MAAM;gBAAW,KAAK;YAA2B;YAC7E;gBAAE,MAAM;gBAAsB,MAAM;gBAAU,KAAK;YAA0B;SAC9E;QACD,WAAW;YACT;gBAAE,MAAM;gBAAiB,MAAM;gBAAc,SAAS;YAAuB;YAC7E;gBAAE,MAAM;gBAAe,MAAM;gBAAc,SAAS;YAA6B;SAClF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,OAAO;QACP,cAAc;QACd,UAAU;QACV,QAAQ;QACR,eAAe,CAAC;QAChB,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBAAE,MAAM;gBAAoB,MAAM;gBAAW,KAAK;YAA4B;YAC9E;gBAAE,MAAM;gBAAuB,MAAM;gBAAY,KAAK;YAAsB;SAC7E;QACD,WAAW;YACT;gBAAE,MAAM;gBAAoB,MAAM;gBAAc,SAAS;YAAuB;YAChF;gBAAE,MAAM;gBAAmB,MAAM;gBAAc,SAAS;YAA6B;SACtF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,OAAO;QACP,cAAc;QACd,UAAU;QACV,QAAQ;QACR,eAAe;QACf,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBAAE,MAAM;gBAAsB,MAAM;gBAAW,KAAK;YAA2B;YAC/E;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,KAAK;YAA2B;SAC/E;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;gBAAc,SAAS;YAAqB;YACzE;gBAAE,MAAM;gBAAiB,MAAM;gBAAc,SAAS;YAA4B;SACnF;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,OAAO;QACP,cAAc;QACd,UAAU;QACV,QAAQ;QACR,eAAe;QACf,OAAO;QACP,SAAS;QACT,WAAW;YACT;gBAAE,MAAM;gBAAoB,MAAM;gBAAW,KAAK;YAA0B;YAC5E;gBAAE,MAAM;gBAAe,MAAM;gBAAU,KAAK;YAAyB;SACtE;QACD,WAAW;YACT;gBAAE,MAAM;gBAAgB,MAAM;gBAAc,SAAS;YAAoB;YACzE;gBAAE,MAAM;gBAAe,MAAM;gBAAc,SAAS;YAA2B;SAChF;IACH;CACD;AAEM,MAAM,iBAAiB;IAC5B;QAAE,IAAI;QAAO,MAAM;QAAa,OAAO,UAAU,MAAM;IAAC;IACxD;QAAE,IAAI;QAAe,MAAM;QAAe,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,eAAe,MAAM;IAAC;IAClH;QAAE,IAAI;QAAQ,MAAM;QAAmB,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,mBAAmB,MAAM;IAAC;IACnH;QAAE,IAAI;QAAW,MAAM;QAAW,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,WAAW,MAAM;IAAC;CACvG;AAEM,MAAM,YAAY;IACvB,YAAY,UAAU,MAAM;IAC5B,kBAAkB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;IAC3E,mBAAmB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;IAC7E,mBAAmB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,GAAG,GAAG,MAAM;IACtG,YAAY,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC9D,qBAAqB,IAAI,QAAQ;AACnC;AAEO,MAAM,oBAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;QACN,QAAQ;IACV;CACD;AAEM,MAAM,uBAAuB;IAClC,OAAO;QACL;YAAE,IAAI;YAAG,MAAM;YAAe,QAAQ;YAAa,UAAU;QAAI;QACjE;YAAE,IAAI;YAAG,MAAM;YAAoB,QAAQ;YAAa,UAAU;QAAK;QACvE;YAAE,IAAI;YAAG,MAAM;YAAgB,QAAQ;YAAa,UAAU;QAAI;QAClE;YAAE,IAAI;YAAG,MAAM;YAAiB,QAAQ;YAAa,UAAU;QAAK;QACpE;YAAE,IAAI;YAAG,MAAM;YAAmB,QAAQ;YAAa,UAAU;QAAI;QACrE;YAAE,IAAI;YAAG,MAAM;YAAmB,QAAQ;YAAa,UAAU;QAAI;KACtE;IACD,eAAe;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;QACP,cAAc;QACd,UAAU;QACV,YAAY;IACd;AACF;AAEO,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;QAC7B,UAAU;YAAE,GAAG;YAAG,GAAG;YAAK,GAAG;QAAE;QAC/B,OAAO;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;QAC1B,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG,CAAC;YAAG,GAAG;YAAG,GAAG,CAAC;QAAE;QAC/B,UAAU;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;QAC7B,OAAO;YAAE,GAAG;YAAK,GAAG;YAAG,GAAG;QAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAG,GAAG;YAAK,GAAG;QAAE;QAC/B,UAAU;YAAE,GAAG,CAAC;YAAK,GAAG;YAAG,GAAG;QAAE;QAChC,OAAO;YAAE,GAAG;YAAK,GAAG;YAAK,GAAG;QAAI;QAChC,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG,CAAC;YAAG,GAAG;YAAG,GAAG;QAAE;QAC9B,UAAU;YAAE,GAAG;YAAG,GAAG;YAAK,GAAG;QAAE;QAC/B,OAAO;YAAE,GAAG;YAAK,GAAG;YAAK,GAAG;QAAI;QAChC,OAAO;QACP,MAAM;IACR;CACD;AAEM,MAAM,YAAY;IACvB;QAAE,IAAI;QAAU,MAAM;QAAU,OAAO;QAAG,OAAO;IAAU;IAC3D;QAAE,IAAI;QAAW,MAAM;QAAW,OAAO;QAAG,OAAO;IAAU;IAC7D;QAAE,IAAI;QAAU,MAAM;QAAe,OAAO;QAAG,OAAO;IAAU;IAChE;QAAE,IAAI;QAAW,MAAM;QAAW,OAAO;QAAG,OAAO;IAAU;CAC9D", "debugId": null}}, {"offset": {"line": 2532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/AIExtractionDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Brain, Check, Zap, Eye, Calendar, DollarSign, Package, Shield } from 'lucide-react';\nimport { demoUploadSimulation } from '@/data/demoData';\n\nconst AIExtractionDemo = ({ onComplete, isActive }) => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [extractedData, setExtractedData] = useState({});\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [confidence, setConfidence] = useState(0);\n\n  const processingSteps = demoUploadSimulation.steps;\n  const finalData = demoUploadSimulation.extractedData;\n\n  useEffect(() => {\n    if (isActive && !isProcessing) {\n      startExtraction();\n    }\n  }, [isActive]);\n\n  const startExtraction = async () => {\n    setIsProcessing(true);\n    setCurrentStep(0);\n    setExtractedData({});\n    setConfidence(0);\n\n    // Process each step\n    for (let i = 0; i < processingSteps.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, processingSteps[i].duration));\n      setCurrentStep(i + 1);\n      \n      // Gradually reveal extracted data\n      if (i >= 2) { // After OCR Analysis\n        const progress = (i - 2) / (processingSteps.length - 3);\n        const dataKeys = Object.keys(finalData);\n        const revealedKeys = dataKeys.slice(0, Math.ceil(dataKeys.length * progress));\n        \n        const partialData = {};\n        revealedKeys.forEach(key => {\n          if (key !== 'confidence') {\n            partialData[key] = finalData[key];\n          }\n        });\n        setExtractedData(partialData);\n        setConfidence(Math.min(finalData.confidence * progress, finalData.confidence));\n      }\n    }\n\n    // Final reveal\n    setExtractedData(finalData);\n    setConfidence(finalData.confidence);\n    setIsProcessing(false);\n    \n    // Auto-complete after showing results\n    setTimeout(() => {\n      onComplete && onComplete();\n    }, 2000);\n  };\n\n  const resetDemo = () => {\n    setCurrentStep(0);\n    setExtractedData({});\n    setIsProcessing(false);\n    setConfidence(0);\n  };\n\n  const getStepIcon = (step, index) => {\n    if (index < currentStep) return <Check className=\"w-4 h-4 text-accent\" />;\n    if (index === currentStep - 1) return <div className=\"w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin\" />;\n    return <div className=\"w-4 h-4 border-2 border-white/30 rounded-full\" />;\n  };\n\n  const dataFields = [\n    { key: 'retailer', label: 'Retailer', icon: <Package className=\"w-4 h-4\" />, color: 'primary' },\n    { key: 'date', label: 'Purchase Date', icon: <Calendar className=\"w-4 h-4\" />, color: 'secondary' },\n    { key: 'total', label: 'Total Amount', icon: <DollarSign className=\"w-4 h-4\" />, color: 'accent' },\n    { key: 'product', label: 'Product', icon: <Package className=\"w-4 h-4\" />, color: 'primary' },\n    { key: 'model', label: 'Model', icon: <Eye className=\"w-4 h-4\" />, color: 'secondary' },\n    { key: 'serialNumber', label: 'Serial Number', icon: <Zap className=\"w-4 h-4\" />, color: 'accent' },\n    { key: 'warranty', label: 'Warranty', icon: <Shield className=\"w-4 h-4\" />, color: 'primary' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Demo Instructions */}\n      <div className=\"bg-secondary/10 border border-secondary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-secondary mb-2\">🧠 AI Extraction Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Watch our advanced AI analyze a receipt and extract warranty information with high accuracy. \n          The system uses OCR, machine learning, and pattern recognition.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-2 gap-6\">\n        \n        {/* Processing Steps */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">AI Processing Pipeline</h4>\n          \n          <div className=\"space-y-3\">\n            {processingSteps.map((step, index) => (\n              <div\n                key={step.id}\n                className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 ${\n                  index < currentStep\n                    ? 'border-accent/30 bg-accent/10'\n                    : index === currentStep - 1\n                    ? 'border-primary/30 bg-primary/10'\n                    : 'border-white/20 bg-white/5'\n                }`}\n              >\n                <div className=\"flex-shrink-0\">\n                  {getStepIcon(step, index)}\n                </div>\n                <div className=\"flex-1\">\n                  <div className={`font-medium text-sm ${\n                    index < currentStep ? 'text-accent' : \n                    index === currentStep - 1 ? 'text-primary' : 'text-white/70'\n                  }`}>\n                    {step.name}\n                  </div>\n                  <div className=\"text-xs text-white/60\">\n                    {index < currentStep ? 'Completed' : \n                     index === currentStep - 1 ? 'Processing...' : 'Pending'}\n                  </div>\n                </div>\n                <div className=\"text-xs text-white/60\">\n                  {step.duration}ms\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Confidence Meter */}\n          <div className=\"bg-white/5 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <span className=\"text-sm font-medium text-white\">Confidence Score</span>\n              <span className=\"text-sm font-bold text-primary\">{confidence.toFixed(1)}%</span>\n            </div>\n            <div className=\"w-full bg-white/20 rounded-full h-2\">\n              <div \n                className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-500\"\n                style={{ width: `${confidence}%` }}\n              ></div>\n            </div>\n            <div className=\"text-xs text-white/60 mt-1\">\n              {confidence >= 95 ? 'Excellent' : \n               confidence >= 85 ? 'Very Good' : \n               confidence >= 75 ? 'Good' : 'Processing...'}\n            </div>\n          </div>\n        </div>\n\n        {/* Extracted Data */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Extracted Information</h4>\n          \n          <div className=\"space-y-3\">\n            {dataFields.map((field) => (\n              <div\n                key={field.key}\n                className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-500 ${\n                  extractedData[field.key]\n                    ? `border-${field.color}/30 bg-${field.color}/10`\n                    : 'border-white/20 bg-white/5'\n                }`}\n              >\n                <div className={`flex-shrink-0 ${\n                  extractedData[field.key] ? `text-${field.color}` : 'text-white/40'\n                }`}>\n                  {field.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"text-sm font-medium text-white/70 mb-1\">\n                    {field.label}\n                  </div>\n                  <div className={`text-sm ${\n                    extractedData[field.key] ? 'text-white' : 'text-white/40'\n                  }`}>\n                    {extractedData[field.key] || 'Analyzing...'}\n                  </div>\n                </div>\n                {extractedData[field.key] && (\n                  <Check className={`w-4 h-4 text-${field.color}`} />\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Sample Receipt Preview */}\n          <div className=\"bg-white/5 rounded-lg p-4\">\n            <h5 className=\"font-medium text-white mb-3\">Sample Receipt</h5>\n            <div className=\"bg-white rounded-lg p-4 text-black text-xs font-mono\">\n              <div className=\"text-center mb-3\">\n                <div className=\"font-bold\">🍎 Apple Store</div>\n                <div>Cupertino, CA</div>\n              </div>\n              <div className=\"border-t border-gray-300 pt-2 space-y-1\">\n                <div className=\"flex justify-between\">\n                  <span>iPhone 15 Pro</span>\n                  <span>$999.99</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Model: A3108</span>\n                  <span></span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Serial: F2LW48XHQM</span>\n                  <span></span>\n                </div>\n                <div className=\"border-t border-gray-300 pt-1 mt-2\">\n                  <div className=\"flex justify-between font-bold\">\n                    <span>Total:</span>\n                    <span>$999.99</span>\n                  </div>\n                </div>\n                <div className=\"text-center mt-2 text-xs\">\n                  <div>Date: 2024-01-15</div>\n                  <div>Warranty: 1 Year Limited</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button\n          onClick={resetDemo}\n          disabled={isProcessing}\n          className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Restart Analysis\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          {!isProcessing && currentStep === processingSteps.length && (\n            <div className=\"flex items-center space-x-2 text-accent\">\n              <Check className=\"w-4 h-4\" />\n              <span className=\"text-sm\">Extraction Complete!</span>\n            </div>\n          )}\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            disabled={isProcessing || currentStep < processingSteps.length}\n            className=\"px-6 py-2 bg-gradient-to-r from-secondary to-primary rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:shadow-secondary/25 transition-all duration-300\"\n          >\n            Continue to Dashboard\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AIExtractionDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,0HAAA,CAAA,uBAAoB,CAAC,KAAK;IAClD,MAAM,YAAY,0HAAA,CAAA,uBAAoB,CAAC,aAAa;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY,CAAC,cAAc;gBAC7B;YACF;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,eAAe;QACf,iBAAiB,CAAC;QAClB,cAAc;QAEd,oBAAoB;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,eAAe,CAAC,EAAE,CAAC,QAAQ;YAC5E,eAAe,IAAI;YAEnB,kCAAkC;YAClC,IAAI,KAAK,GAAG;gBACV,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,MAAM,GAAG,CAAC;gBACtD,MAAM,WAAW,OAAO,IAAI,CAAC;gBAC7B,MAAM,eAAe,SAAS,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;gBAEnE,MAAM,cAAc,CAAC;gBACrB,aAAa,OAAO,CAAC,CAAA;oBACnB,IAAI,QAAQ,cAAc;wBACxB,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;oBACnC;gBACF;gBACA,iBAAiB;gBACjB,cAAc,KAAK,GAAG,CAAC,UAAU,UAAU,GAAG,UAAU,UAAU,UAAU;YAC9E;QACF;QAEA,eAAe;QACf,iBAAiB;QACjB,cAAc,UAAU,UAAU;QAClC,gBAAgB;QAEhB,sCAAsC;QACtC,WAAW;YACT,cAAc;QAChB,GAAG;IACL;IAEA,MAAM,YAAY;QAChB,eAAe;QACf,iBAAiB,CAAC;QAClB,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC,MAAM;QACzB,IAAI,QAAQ,aAAa,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,UAAU,cAAc,GAAG,qBAAO,6LAAC;YAAI,WAAU;;;;;;QACrD,qBAAO,6LAAC;YAAI,WAAU;;;;;;IACxB;IAEA,MAAM,aAAa;QACjB;YAAE,KAAK;YAAY,OAAO;YAAY,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAU;QAC9F;YAAE,KAAK;YAAQ,OAAO;YAAiB,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAY;QAClG;YAAE,KAAK;YAAS,OAAO;YAAgB,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAS;QACjG;YAAE,KAAK;YAAW,OAAO;YAAW,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAU;QAC5F;YAAE,KAAK;YAAS,OAAO;YAAS,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAY;QACtF;YAAE,KAAK;YAAgB,OAAO;YAAiB,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAS;QAClG;YAAE,KAAK;YAAY,OAAO;YAAY,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAU;KAC9F;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;wCAEC,WAAW,CAAC,8EAA8E,EACxF,QAAQ,cACJ,kCACA,UAAU,cAAc,IACxB,oCACA,8BACJ;;0DAEF,6LAAC;gDAAI,WAAU;0DACZ,YAAY,MAAM;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,oBAAoB,EACnC,QAAQ,cAAc,gBACtB,UAAU,cAAc,IAAI,iBAAiB,iBAC7C;kEACC,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,cAAc,cACtB,UAAU,cAAc,IAAI,kBAAkB;;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ;oDAAC;;;;;;;;uCAzBZ,KAAK,EAAE;;;;;;;;;;0CAgClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,6LAAC;gDAAK,WAAU;;oDAAkC,WAAW,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,WAAW,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAGrC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,KAAK,cACnB,cAAc,KAAK,cACnB,cAAc,KAAK,SAAS;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC;wCAEC,WAAW,CAAC,8EAA8E,EACxF,aAAa,CAAC,MAAM,GAAG,CAAC,GACpB,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,GAAG,CAAC,GAC/C,8BACJ;;0DAEF,6LAAC;gDAAI,WAAW,CAAC,cAAc,EAC7B,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE,GAAG,iBACnD;0DACC,MAAM,IAAI;;;;;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EACvB,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,eAAe,iBAC1C;kEACC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI;;;;;;;;;;;;4CAGhC,aAAa,CAAC,MAAM,GAAG,CAAC,kBACvB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,aAAa,EAAE,MAAM,KAAK,EAAE;;;;;;;uCAvB5C,MAAM,GAAG;;;;;;;;;;0CA8BpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAY;;;;;;kEAC3B,6LAAC;kEAAI;;;;;;;;;;;;0DAEP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;;;;;;;;;;kEAEH,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;;;;;;;;;;kEAEH,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAI;;;;;;0EACL,6LAAC;0EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;4BACZ,CAAC,gBAAgB,gBAAgB,gBAAgB,MAAM,kBACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU,gBAAgB,cAAc,gBAAgB,MAAM;gCAC9D,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA1PM;KAAA;uCA4PS", "debugId": null}}, {"offset": {"line": 3225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/DashboardDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  BarChart3, \n  Calendar, \n  Filter, \n  Search, \n  Bell, \n  Shield, \n  AlertTriangle, \n  CheckCircle,\n  Clock,\n  DollarSign,\n  Package,\n  Eye\n} from 'lucide-react';\nimport { demoItems, demoStats, demoCategories, demoNotifications } from '@/data/demoData';\n\nconst DashboardDemo = ({ onComplete, isActive }) => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState('expiry');\n\n  const filteredItems = demoItems.filter(item => {\n    const matchesCategory = selectedCategory === 'all' || \n      item.category.toLowerCase().includes(selectedCategory);\n    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      item.brand.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  const getStatusColor = (status, daysRemaining) => {\n    if (status === 'Expired') return 'red-500';\n    if (daysRemaining <= 30) return 'yellow-500';\n    return 'green-500';\n  };\n\n  const getStatusIcon = (status, daysRemaining) => {\n    if (status === 'Expired') return <AlertTriangle className=\"w-4 h-4\" />;\n    if (daysRemaining <= 30) return <Clock className=\"w-4 h-4\" />;\n    return <CheckCircle className=\"w-4 h-4\" />;\n  };\n\n  const tabs = [\n    { id: 'overview', name: 'Overview', icon: <BarChart3 className=\"w-4 h-4\" /> },\n    { id: 'items', name: 'Items', icon: <Package className=\"w-4 h-4\" /> },\n    { id: 'calendar', name: 'Calendar', icon: <Calendar className=\"w-4 h-4\" /> },\n    { id: 'notifications', name: 'Alerts', icon: <Bell className=\"w-4 h-4\" /> }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Demo Instructions */}\n      <div className=\"bg-primary/10 border border-primary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-primary mb-2\">📊 Dashboard Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Explore the warranty management dashboard. Navigate through different sections to see \n          how you can track, organize, and manage all your warranties in one place.\n        </p>\n      </div>\n\n      {/* Dashboard Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"font-heading text-2xl font-bold text-white\">Warranty Dashboard</h2>\n          <p className=\"text-white/70\">Manage all your warranties and coverage</p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <button className=\"p-2 bg-white/5 hover:bg-white/10 border border-white/20 rounded-lg transition-all duration-300\">\n            <Bell className=\"w-5 h-5 text-white\" />\n          </button>\n          <button className=\"px-4 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg text-white hover:shadow-lg hover:shadow-primary/25 transition-all duration-300\">\n            Add Item\n          </button>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-white/5 rounded-lg p-1\">\n        {tabs.map((tab) => (\n          <button\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-300 ${\n              activeTab === tab.id\n                ? 'bg-primary text-white shadow-lg'\n                : 'text-white/70 hover:text-white hover:bg-white/10'\n            }`}\n          >\n            {tab.icon}\n            <span>{tab.name}</span>\n          </button>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"min-h-96\">\n        \n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Package className=\"w-5 h-5 text-primary\" />\n                  <span className=\"text-2xl font-bold text-white\">{demoStats.totalItems}</span>\n                </div>\n                <div className=\"text-sm text-white/70\">Total Items</div>\n              </div>\n              \n              <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Shield className=\"w-5 h-5 text-accent\" />\n                  <span className=\"text-2xl font-bold text-white\">{demoStats.activeWarranties}</span>\n                </div>\n                <div className=\"text-sm text-white/70\">Active Warranties</div>\n              </div>\n              \n              <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <AlertTriangle className=\"w-5 h-5 text-yellow-500\" />\n                  <span className=\"text-2xl font-bold text-white\">{demoStats.expiringThisMonth}</span>\n                </div>\n                <div className=\"text-sm text-white/70\">Expiring Soon</div>\n              </div>\n              \n              <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <DollarSign className=\"w-5 h-5 text-secondary\" />\n                  <span className=\"text-2xl font-bold text-white\">\n                    ${demoStats.totalValue.toLocaleString()}\n                  </span>\n                </div>\n                <div className=\"text-sm text-white/70\">Total Value</div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n              <h3 className=\"font-semibold text-white mb-4\">Recent Activity</h3>\n              <div className=\"space-y-3\">\n                {demoNotifications.slice(0, 3).map((notification) => (\n                  <div key={notification.id} className=\"flex items-start space-x-3 p-3 bg-white/5 rounded-lg\">\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\n                      notification.type === 'warning' ? 'bg-yellow-500' :\n                      notification.type === 'error' ? 'bg-red-500' :\n                      notification.type === 'success' ? 'bg-green-500' : 'bg-blue-500'\n                    }`}></div>\n                    <div className=\"flex-1\">\n                      <div className=\"text-white text-sm font-medium\">{notification.title}</div>\n                      <div className=\"text-white/70 text-xs\">{notification.message}</div>\n                      <div className=\"text-white/50 text-xs mt-1\">{notification.date}</div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Items Tab */}\n        {activeTab === 'items' && (\n          <div className=\"space-y-4\">\n            {/* Filters */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <div className=\"flex-1 relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search items...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300\"\n                />\n              </div>\n              \n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white focus:outline-none focus:border-primary transition-colors duration-300\"\n              >\n                {demoCategories.map((category) => (\n                  <option key={category.id} value={category.id} className=\"bg-surface text-white\">\n                    {category.name} ({category.count})\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Items Grid */}\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {filteredItems.map((item) => (\n                <div key={item.id} className=\"bg-white/5 border border-white/10 rounded-lg p-4 hover:border-white/20 transition-all duration-300\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div>\n                      <h4 className=\"font-semibold text-white text-sm\">{item.name}</h4>\n                      <p className=\"text-white/70 text-xs\">{item.brand} • {item.model}</p>\n                    </div>\n                    <div className={`flex items-center space-x-1 text-${getStatusColor(item.status, item.daysRemaining)}`}>\n                      {getStatusIcon(item.status, item.daysRemaining)}\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2 text-xs\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/60\">Warranty:</span>\n                      <span className=\"text-white\">{item.warrantyDuration}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/60\">Expires:</span>\n                      <span className=\"text-white\">{item.warrantyExpiry}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/60\">Days Left:</span>\n                      <span className={`text-${getStatusColor(item.status, item.daysRemaining)}`}>\n                        {item.daysRemaining > 0 ? item.daysRemaining : 'Expired'}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <button className=\"w-full mt-3 px-3 py-1 bg-primary/20 hover:bg-primary/30 border border-primary/30 rounded text-primary text-xs transition-all duration-300\">\n                    View Details\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Calendar Tab */}\n        {activeTab === 'calendar' && (\n          <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n            <h3 className=\"font-semibold text-white mb-4\">Warranty Calendar</h3>\n            <div className=\"text-center py-12\">\n              <Calendar className=\"w-16 h-16 text-white/40 mx-auto mb-4\" />\n              <p className=\"text-white/70\">Calendar view coming soon...</p>\n              <p className=\"text-white/50 text-sm\">Track expiration dates and service schedules</p>\n            </div>\n          </div>\n        )}\n\n        {/* Notifications Tab */}\n        {activeTab === 'notifications' && (\n          <div className=\"space-y-4\">\n            {demoNotifications.map((notification) => (\n              <div key={notification.id} className={`border rounded-lg p-4 ${\n                notification.read ? 'border-white/10 bg-white/5' : 'border-primary/30 bg-primary/10'\n              }`}>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\n                      notification.type === 'warning' ? 'bg-yellow-500' :\n                      notification.type === 'error' ? 'bg-red-500' :\n                      notification.type === 'success' ? 'bg-green-500' : 'bg-blue-500'\n                    }`}></div>\n                    <div>\n                      <h4 className=\"font-semibold text-white text-sm\">{notification.title}</h4>\n                      <p className=\"text-white/70 text-sm\">{notification.message}</p>\n                      <p className=\"text-white/50 text-xs mt-1\">{notification.date}</p>\n                    </div>\n                  </div>\n                  <button className=\"text-white/60 hover:text-white\">\n                    <Eye className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button\n          onClick={() => setActiveTab('overview')}\n          className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n        >\n          Reset View\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex items-center space-x-2 text-accent\">\n            <CheckCircle className=\"w-4 h-4\" />\n            <span className=\"text-sm\">Dashboard Explored!</span>\n          </div>\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            className=\"px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg text-white hover:shadow-lg hover:shadow-primary/25 transition-all duration-300\"\n          >\n            Continue to Reminders\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAjBA;;;;AAmBA,MAAM,gBAAgB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,gBAAgB,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA;QACrC,MAAM,kBAAkB,qBAAqB,SAC3C,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QACvC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3E,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC1D,OAAO,mBAAmB;IAC5B;IAEA,MAAM,iBAAiB,CAAC,QAAQ;QAC9B,IAAI,WAAW,WAAW,OAAO;QACjC,IAAI,iBAAiB,IAAI,OAAO;QAChC,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,QAAQ;QAC7B,IAAI,WAAW,WAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC1D,IAAI,iBAAiB,IAAI,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,MAAM;YAAY,oBAAM,6LAAC,qNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;QAC5E;YAAE,IAAI;YAAS,MAAM;YAAS,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAAa;QACpE;YAAE,IAAI;YAAY,MAAM;YAAY,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAAa;QAC3E;YAAE,IAAI;YAAiB,MAAM;YAAU,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QAAa;KAC3E;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;;;;;;;0BAOvK,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;wBAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wBAClC,WAAW,CAAC,6EAA6E,EACvF,cAAc,IAAI,EAAE,GAChB,oCACA,oDACJ;;4BAED,IAAI,IAAI;0CACT,6LAAC;0CAAM,IAAI,IAAI;;;;;;;uBATV,IAAI,EAAE;;;;;;;;;;0BAejB,6LAAC;gBAAI,WAAU;;oBAGZ,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAiC,0HAAA,CAAA,YAAS,CAAC,UAAU;;;;;;;;;;;;0DAEvE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAiC,0HAAA,CAAA,YAAS,CAAC,gBAAgB;;;;;;;;;;;;0DAE7E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAK,WAAU;kEAAiC,0HAAA,CAAA,YAAS,CAAC,iBAAiB;;;;;;;;;;;;0DAE9E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAGzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;;4DAAgC;4DAC5C,0HAAA,CAAA,YAAS,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;;0DAGzC,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;kDACZ,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAClC,6LAAC;gDAA0B,WAAU;;kEACnC,6LAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,aAAa,IAAI,KAAK,YAAY,kBAClC,aAAa,IAAI,KAAK,UAAU,eAChC,aAAa,IAAI,KAAK,YAAY,iBAAiB,eACnD;;;;;;kEACF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAkC,aAAa,KAAK;;;;;;0EACnE,6LAAC;gEAAI,WAAU;0EAAyB,aAAa,OAAO;;;;;;0EAC5D,6LAAC;gEAAI,WAAU;0EAA8B,aAAa,IAAI;;;;;;;;;;;;;+CATxD,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAmBlC,cAAc,yBACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAId,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;kDAET,0HAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,yBACnB,6LAAC;gDAAyB,OAAO,SAAS,EAAE;gDAAE,WAAU;;oDACrD,SAAS,IAAI;oDAAC;oDAAG,SAAS,KAAK;oDAAC;;+CADtB,SAAS,EAAE;;;;;;;;;;;;;;;;0CAQ9B,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC,KAAK,IAAI;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;;oEAAyB,KAAK,KAAK;oEAAC;oEAAI,KAAK,KAAK;;;;;;;;;;;;;kEAEjE,6LAAC;wDAAI,WAAW,CAAC,iCAAiC,EAAE,eAAe,KAAK,MAAM,EAAE,KAAK,aAAa,GAAG;kEAClG,cAAc,KAAK,MAAM,EAAE,KAAK,aAAa;;;;;;;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAc,KAAK,gBAAgB;;;;;;;;;;;;kEAErD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAc,KAAK,cAAc;;;;;;;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAW,CAAC,KAAK,EAAE,eAAe,KAAK,MAAM,EAAE,KAAK,aAAa,GAAG;0EACvE,KAAK,aAAa,GAAG,IAAI,KAAK,aAAa,GAAG;;;;;;;;;;;;;;;;;;0DAKrD,6LAAC;gDAAO,WAAU;0DAA4I;;;;;;;uCA5BtJ,KAAK,EAAE;;;;;;;;;;;;;;;;oBAsCxB,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAM1C,cAAc,iCACb,6LAAC;wBAAI,WAAU;kCACZ,0HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,6BACtB,6LAAC;gCAA0B,WAAW,CAAC,sBAAsB,EAC3D,aAAa,IAAI,GAAG,+BAA+B,mCACnD;0CACA,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,0BAA0B,EACzC,aAAa,IAAI,KAAK,YAAY,kBAClC,aAAa,IAAI,KAAK,UAAU,eAChC,aAAa,IAAI,KAAK,YAAY,iBAAiB,eACnD;;;;;;8DACF,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAoC,aAAa,KAAK;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAAyB,aAAa,OAAO;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEAA8B,aAAa,IAAI;;;;;;;;;;;;;;;;;;sDAGhE,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;+BAjBX,aAAa,EAAE;;;;;;;;;;;;;;;;0BA2BjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxRM;KAAA;uCA0RS", "debugId": null}}, {"offset": {"line": 4153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/ReminderSystemDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Bell, Calendar, Clock, Check, Settings, Smartphone } from 'lucide-react';\n\nconst ReminderSystemDemo = ({ onComplete, isActive }) => {\n  const [activeReminders, setActiveReminders] = useState([]);\n  const [reminderSettings, setReminderSettings] = useState({\n    email: true,\n    push: true,\n    sms: false,\n    advanceNotice: 30\n  });\n\n  const sampleReminders = [\n    {\n      id: 1,\n      title: \"iPhone 15 Pro Warranty Expiring\",\n      message: \"Your iPhone warranty expires in 30 days\",\n      type: \"warranty_expiry\",\n      priority: \"high\",\n      date: \"2024-12-15\",\n      item: \"iPhone 15 Pro\"\n    },\n    {\n      id: 2,\n      title: \"Tesla Service Due\",\n      message: \"Annual service appointment recommended\",\n      type: \"service_due\",\n      priority: \"medium\",\n      date: \"2024-12-20\",\n      item: \"Tesla Model 3\"\n    },\n    {\n      id: 3,\n      title: \"Filter Replacement\",\n      message: \"Samsung refrigerator water filter needs replacement\",\n      type: \"maintenance\",\n      priority: \"low\",\n      date: \"2024-12-30\",\n      item: \"Samsung Refrigerator\"\n    }\n  ];\n\n  useEffect(() => {\n    if (isActive) {\n      // Simulate receiving reminders\n      const timer = setTimeout(() => {\n        setActiveReminders(sampleReminders);\n        setTimeout(() => onComplete && onComplete(), 3000);\n      }, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, onComplete]);\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'red-500';\n      case 'medium': return 'yellow-500';\n      case 'low': return 'green-500';\n      default: return 'blue-500';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-accent/10 border border-accent/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-accent mb-2\">🔔 Smart Reminders Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Experience intelligent notifications that help you never miss important warranty deadlines or service appointments.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-2 gap-6\">\n        {/* Reminder Settings */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Notification Preferences</h4>\n          \n          <div className=\"bg-white/5 border border-white/10 rounded-lg p-4 space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <Bell className=\"w-5 h-5 text-primary\" />\n                <span className=\"text-white\">Email Notifications</span>\n              </div>\n              <button\n                onClick={() => setReminderSettings(prev => ({ ...prev, email: !prev.email }))}\n                className={`w-12 h-6 rounded-full transition-all duration-300 ${\n                  reminderSettings.email ? 'bg-primary' : 'bg-white/20'\n                }`}\n              >\n                <div className={`w-5 h-5 bg-white rounded-full transition-all duration-300 ${\n                  reminderSettings.email ? 'translate-x-6' : 'translate-x-0.5'\n                }`}></div>\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <Smartphone className=\"w-5 h-5 text-secondary\" />\n                <span className=\"text-white\">Push Notifications</span>\n              </div>\n              <button\n                onClick={() => setReminderSettings(prev => ({ ...prev, push: !prev.push }))}\n                className={`w-12 h-6 rounded-full transition-all duration-300 ${\n                  reminderSettings.push ? 'bg-secondary' : 'bg-white/20'\n                }`}\n              >\n                <div className={`w-5 h-5 bg-white rounded-full transition-all duration-300 ${\n                  reminderSettings.push ? 'translate-x-6' : 'translate-x-0.5'\n                }`}></div>\n              </button>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-white text-sm\">Advance Notice (days)</label>\n              <input\n                type=\"range\"\n                min=\"7\"\n                max=\"90\"\n                value={reminderSettings.advanceNotice}\n                onChange={(e) => setReminderSettings(prev => ({ ...prev, advanceNotice: parseInt(e.target.value) }))}\n                className=\"w-full\"\n              />\n              <div className=\"text-white/70 text-sm\">{reminderSettings.advanceNotice} days before expiry</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Active Reminders */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Active Reminders</h4>\n          \n          <div className=\"space-y-3\">\n            {activeReminders.map((reminder) => (\n              <div key={reminder.id} className=\"bg-white/5 border border-white/10 rounded-lg p-4 hover:border-white/20 transition-all duration-300\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-3 h-3 rounded-full mt-1 bg-${getPriorityColor(reminder.priority)}`}></div>\n                    <div>\n                      <h5 className=\"font-semibold text-white text-sm\">{reminder.title}</h5>\n                      <p className=\"text-white/70 text-xs\">{reminder.message}</p>\n                    </div>\n                  </div>\n                  <span className={`text-xs px-2 py-1 rounded-full bg-${getPriorityColor(reminder.priority)}/20 text-${getPriorityColor(reminder.priority)}`}>\n                    {reminder.priority}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-xs\">\n                  <span className=\"text-white/60\">{reminder.item}</span>\n                  <span className=\"text-white/60\">{reminder.date}</span>\n                </div>\n                \n                <div className=\"flex space-x-2 mt-3\">\n                  <button className=\"flex-1 px-3 py-1 bg-primary/20 hover:bg-primary/30 border border-primary/30 rounded text-primary text-xs transition-all duration-300\">\n                    Snooze\n                  </button>\n                  <button className=\"flex-1 px-3 py-1 bg-accent/20 hover:bg-accent/30 border border-accent/30 rounded text-accent text-xs transition-all duration-300\">\n                    Mark Done\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\">\n          Customize Settings\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          {activeReminders.length > 0 && (\n            <div className=\"flex items-center space-x-2 text-accent\">\n              <Check className=\"w-4 h-4\" />\n              <span className=\"text-sm\">Reminders Active!</span>\n            </div>\n          )}\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            className=\"px-6 py-2 bg-gradient-to-r from-accent to-primary rounded-lg text-white hover:shadow-lg hover:shadow-accent/25 transition-all duration-300\"\n          >\n            Continue to 3D View\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReminderSystemDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,qBAAqB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,OAAO;QACP,MAAM;QACN,KAAK;QACL,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,MAAM;QACR;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU;gBACZ,+BAA+B;gBAC/B,MAAM,QAAQ;0DAAW;wBACvB,mBAAmB;wBACnB;kEAAW,IAAM,cAAc;iEAAc;oBAC/C;yDAAG;gBACH;oDAAO,IAAM,aAAa;;YAC5B;QACF;uCAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAa;;;;;;;;;;;;0DAE/B,6LAAC;gDACC,SAAS,IAAM,oBAAoB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,CAAC,KAAK,KAAK;wDAAC,CAAC;gDAC3E,WAAW,CAAC,kDAAkD,EAC5D,iBAAiB,KAAK,GAAG,eAAe,eACxC;0DAEF,cAAA,6LAAC;oDAAI,WAAW,CAAC,0DAA0D,EACzE,iBAAiB,KAAK,GAAG,kBAAkB,mBAC3C;;;;;;;;;;;;;;;;;kDAIN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAa;;;;;;;;;;;;0DAE/B,6LAAC;gDACC,SAAS,IAAM,oBAAoB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,CAAC,KAAK,IAAI;wDAAC,CAAC;gDACzE,WAAW,CAAC,kDAAkD,EAC5D,iBAAiB,IAAI,GAAG,iBAAiB,eACzC;0DAEF,cAAA,6LAAC;oDAAI,WAAW,CAAC,0DAA0D,EACzE,iBAAiB,IAAI,GAAG,kBAAkB,mBAC1C;;;;;;;;;;;;;;;;;kDAIN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAqB;;;;;;0DACtC,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,iBAAiB,aAAa;gDACrC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAClG,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;oDAAyB,iBAAiB,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;wCAAsB,WAAU;;0DAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,SAAS,QAAQ,GAAG;;;;;;0EACrF,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAoC,SAAS,KAAK;;;;;;kFAChE,6LAAC;wEAAE,WAAU;kFAAyB,SAAS,OAAO;;;;;;;;;;;;;;;;;;kEAG1D,6LAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,SAAS,QAAQ,EAAE,SAAS,EAAE,iBAAiB,SAAS,QAAQ,GAAG;kEACvI,SAAS,QAAQ;;;;;;;;;;;;0DAItB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiB,SAAS,IAAI;;;;;;kEAC9C,6LAAC;wDAAK,WAAU;kEAAiB,SAAS,IAAI;;;;;;;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAAuI;;;;;;kEAGzJ,6LAAC;wDAAO,WAAU;kEAAmI;;;;;;;;;;;;;uCAvB/I,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAiC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;kCAA+H;;;;;;kCAIjJ,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAzLM;KAAA;uCA2LS", "debugId": null}}, {"offset": {"line": 4665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/Inventory3DDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { <PERSON>vas, useFrame } from '@react-three/fiber';\nimport { OrbitControls, Box as ThreeBox, Sphere, Cylinder } from '@react-three/drei';\nimport { Box as BoxIcon, RotateCcw, ZoomIn, Eye, Home, Check } from 'lucide-react';\nimport { demo3DItems, demoRooms } from '@/data/demoData';\n\n// 3D Item Component\nfunction Item3D({ item, isSelected, onClick }) {\n  const meshRef = useRef();\n  \n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y += 0.01;\n      if (isSelected) {\n        meshRef.current.scale.setScalar(1.1 + Math.sin(state.clock.elapsedTime * 2) * 0.1);\n      }\n    }\n  });\n\n  const getGeometry = () => {\n    switch (item.name) {\n      case 'iPhone 15 Pro':\n        return <ThreeBox args={[0.3, 0.6, 0.05]} />;\n      case 'Samsung Refrigerator':\n        return <ThreeBox args={[1, 2, 0.8]} />;\n      case 'MacBook Pro':\n        return <ThreeBox args={[1.2, 0.8, 0.1]} />;\n      case 'Dyson V15':\n        return <Cylinder args={[0.1, 0.1, 1.2]} />;\n      default:\n        return <ThreeBox args={[0.5, 0.5, 0.5]} />;\n    }\n  };\n\n  return (\n    <mesh\n      ref={meshRef}\n      position={[item.position.x, item.position.y, item.position.z]}\n      rotation={[item.rotation.x, item.rotation.y, item.rotation.z]}\n      onClick={onClick}\n      scale={isSelected ? 1.2 : 1}\n    >\n      {getGeometry()}\n      <meshStandardMaterial \n        color={isSelected ? '#00D4FF' : item.color} \n        transparent \n        opacity={0.8}\n        emissive={isSelected ? '#00D4FF' : '#000000'}\n        emissiveIntensity={isSelected ? 0.2 : 0}\n      />\n    </mesh>\n  );\n}\n\n// Room Environment\nfunction RoomEnvironment() {\n  return (\n    <>\n      {/* Floor */}\n      <mesh position={[0, -1, 0]} rotation={[-Math.PI / 2, 0, 0]}>\n        <planeGeometry args={[20, 20]} />\n        <meshStandardMaterial color=\"#1a1a2e\" transparent opacity={0.3} />\n      </mesh>\n      \n      {/* Walls */}\n      <mesh position={[0, 2, -5]} rotation={[0, 0, 0]}>\n        <planeGeometry args={[20, 6]} />\n        <meshStandardMaterial color=\"#0a0a0f\" transparent opacity={0.2} />\n      </mesh>\n      \n      {/* Lighting */}\n      <ambientLight intensity={0.4} />\n      <pointLight position={[5, 5, 5]} intensity={0.8} color=\"#00D4FF\" />\n      <pointLight position={[-5, 5, -5]} intensity={0.6} color=\"#8B5CF6\" />\n    </>\n  );\n}\n\nconst Inventory3DDemo = ({ onComplete, isActive }) => {\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [selectedRoom, setSelectedRoom] = useState('office');\n  const [viewMode, setViewMode] = useState('3d'); // 3d, ar, list\n\n  useEffect(() => {\n    if (isActive) {\n      // Auto-complete after user interaction\n      const timer = setTimeout(() => {\n        onComplete && onComplete();\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, onComplete]);\n\n  const filteredItems = demo3DItems.filter(item => \n    selectedRoom === 'all' || item.room === selectedRoom\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-secondary/10 border border-secondary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-secondary mb-2\">🏠 3D Inventory Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Visualize your items in 3D space. Click on items to see details, switch between rooms, \n          and experience the future of inventory management.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-4 gap-6\">\n        \n        {/* Controls Sidebar */}\n        <div className=\"space-y-4\">\n          <div>\n            <h4 className=\"font-semibold text-white mb-3\">Room Selection</h4>\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setSelectedRoom('all')}\n                className={`w-full text-left px-3 py-2 rounded-lg border transition-all duration-300 ${\n                  selectedRoom === 'all' \n                    ? 'border-primary bg-primary/20 text-primary' \n                    : 'border-white/20 text-white/70 hover:border-white/40'\n                }`}\n              >\n                All Rooms ({demo3DItems.length})\n              </button>\n              {demoRooms.map((room) => (\n                <button\n                  key={room.id}\n                  onClick={() => setSelectedRoom(room.id)}\n                  className={`w-full text-left px-3 py-2 rounded-lg border transition-all duration-300 ${\n                    selectedRoom === room.id \n                      ? 'border-primary bg-primary/20 text-primary' \n                      : 'border-white/20 text-white/70 hover:border-white/40'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <span>{room.name}</span>\n                    <span className=\"text-xs\">({room.items})</span>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div>\n            <h4 className=\"font-semibold text-white mb-3\">View Mode</h4>\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setViewMode('3d')}\n                className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all duration-300 ${\n                  viewMode === '3d' \n                    ? 'border-secondary bg-secondary/20 text-secondary' \n                    : 'border-white/20 text-white/70 hover:border-white/40'\n                }`}\n              >\n                <BoxIcon className=\"w-4 h-4\" />\n                <span>3D View</span>\n              </button>\n              <button\n                onClick={() => setViewMode('ar')}\n                className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all duration-300 ${\n                  viewMode === 'ar' \n                    ? 'border-accent bg-accent/20 text-accent' \n                    : 'border-white/20 text-white/70 hover:border-white/40'\n                }`}\n              >\n                <Eye className=\"w-4 h-4\" />\n                <span>AR Preview</span>\n              </button>\n            </div>\n          </div>\n\n          {selectedItem && (\n            <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n              <h4 className=\"font-semibold text-white mb-2\">Selected Item</h4>\n              <div className=\"space-y-2 text-sm\">\n                <div className=\"text-white\">{selectedItem.name}</div>\n                <div className=\"text-white/70\">Room: {selectedItem.room}</div>\n                <div className=\"text-white/70\">\n                  Position: ({selectedItem.position.x}, {selectedItem.position.y}, {selectedItem.position.z})\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 3D Viewport */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white/5 border border-white/10 rounded-lg overflow-hidden\">\n            <div className=\"flex items-center justify-between p-4 border-b border-white/10\">\n              <h4 className=\"font-semibold text-white\">\n                {viewMode === '3d' ? '3D Inventory View' : 'AR Preview Mode'}\n              </h4>\n              <div className=\"flex items-center space-x-2\">\n                <button className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300\">\n                  <RotateCcw className=\"w-4 h-4 text-white\" />\n                </button>\n                <button className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300\">\n                  <ZoomIn className=\"w-4 h-4 text-white\" />\n                </button>\n                <button className=\"p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300\">\n                  <Home className=\"w-4 h-4 text-white\" />\n                </button>\n              </div>\n            </div>\n\n            <div className=\"h-96 relative\">\n              {viewMode === '3d' ? (\n                <Canvas camera={{ position: [5, 5, 5], fov: 60 }}>\n                  <RoomEnvironment />\n                  <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />\n                  \n                  {filteredItems.map((item) => (\n                    <Item3D\n                      key={item.id}\n                      item={item}\n                      isSelected={selectedItem?.id === item.id}\n                      onClick={() => setSelectedItem(item)}\n                    />\n                  ))}\n                </Canvas>\n              ) : (\n                <div className=\"h-full flex items-center justify-center bg-gradient-to-br from-accent/20 to-primary/20\">\n                  <div className=\"text-center\">\n                    <Eye className=\"w-16 h-16 text-accent mx-auto mb-4\" />\n                    <h3 className=\"text-xl font-semibold text-white mb-2\">AR Preview Mode</h3>\n                    <p className=\"text-white/70 mb-4\">Point your camera to see items in real space</p>\n                    <button className=\"px-6 py-2 bg-accent/20 border border-accent/30 rounded-lg text-accent hover:bg-accent/30 transition-all duration-300\">\n                      Enable Camera\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Item List */}\n            <div className=\"p-4 border-t border-white/10\">\n              <h5 className=\"font-semibold text-white mb-3\">Items in View</h5>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n                {filteredItems.map((item) => (\n                  <button\n                    key={item.id}\n                    onClick={() => setSelectedItem(item)}\n                    className={`p-2 rounded-lg border text-xs transition-all duration-300 ${\n                      selectedItem?.id === item.id\n                        ? 'border-primary bg-primary/20 text-primary'\n                        : 'border-white/20 text-white/70 hover:border-white/40'\n                    }`}\n                  >\n                    {item.name}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button\n          onClick={() => {\n            setSelectedItem(null);\n            setSelectedRoom('office');\n            setViewMode('3d');\n          }}\n          className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n        >\n          Reset View\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex items-center space-x-2 text-secondary\">\n            <Check className=\"w-4 h-4\" />\n            <span className=\"text-sm\">3D Experience Complete!</span>\n          </div>\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            className=\"px-6 py-2 bg-gradient-to-r from-secondary to-accent rounded-lg text-white hover:shadow-lg hover:shadow-secondary/25 transition-all duration-300\"\n          >\n            Continue to Claims\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Inventory3DDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,oBAAoB;AACpB,SAAS,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE;;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAErB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;2BAAE,CAAC;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,IAAI,YAAY;oBACd,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBAChF;YACF;QACF;;IAEA,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,qBAAO,6LAAC,6JAAA,CAAA,MAAQ;oBAAC,MAAM;wBAAC;wBAAK;wBAAK;qBAAK;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC,6JAAA,CAAA,MAAQ;oBAAC,MAAM;wBAAC;wBAAG;wBAAG;qBAAI;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,6JAAA,CAAA,MAAQ;oBAAC,MAAM;wBAAC;wBAAK;wBAAK;qBAAI;;;;;;YACxC,KAAK;gBACH,qBAAO,6LAAC,6JAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAC;wBAAK;wBAAK;qBAAI;;;;;;YACxC;gBACE,qBAAO,6LAAC,6JAAA,CAAA,MAAQ;oBAAC,MAAM;wBAAC;wBAAK;wBAAK;qBAAI;;;;;;QAC1C;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,UAAU;YAAC,KAAK,QAAQ,CAAC,CAAC;YAAE,KAAK,QAAQ,CAAC,CAAC;YAAE,KAAK,QAAQ,CAAC,CAAC;SAAC;QAC7D,UAAU;YAAC,KAAK,QAAQ,CAAC,CAAC;YAAE,KAAK,QAAQ,CAAC,CAAC;YAAE,KAAK,QAAQ,CAAC,CAAC;SAAC;QAC7D,SAAS;QACT,OAAO,aAAa,MAAM;;YAEzB;0BACD,6LAAC;gBACC,OAAO,aAAa,YAAY,KAAK,KAAK;gBAC1C,WAAW;gBACX,SAAS;gBACT,UAAU,aAAa,YAAY;gBACnC,mBAAmB,aAAa,MAAM;;;;;;;;;;;;AAI9C;GA7CS;;QAGP,kNAAA,CAAA,WAAQ;;;KAHD;AA+CT,mBAAmB;AACnB,SAAS;IACP,qBACE;;0BAEE,6LAAC;gBAAK,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;gBAAE,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;;kCACxD,6LAAC;wBAAc,MAAM;4BAAC;4BAAI;yBAAG;;;;;;kCAC7B,6LAAC;wBAAqB,OAAM;wBAAU,WAAW;wBAAC,SAAS;;;;;;;;;;;;0BAI7D,6LAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;;kCAC7C,6LAAC;wBAAc,MAAM;4BAAC;4BAAI;yBAAE;;;;;;kCAC5B,6LAAC;wBAAqB,OAAM;wBAAU,WAAW;wBAAC,SAAS;;;;;;;;;;;;0BAI7D,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACvD,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;;;AAG/D;MArBS;AAuBT,MAAM,kBAAkB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,eAAe;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU;gBACZ,uCAAuC;gBACvC,MAAM,QAAQ;uDAAW;wBACvB,cAAc;oBAChB;sDAAG;gBACH;iDAAO,IAAM,aAAa;;YAC5B;QACF;oCAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,gBAAgB,0HAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,OACvC,iBAAiB,SAAS,KAAK,IAAI,KAAK;IAG1C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,CAAC,yEAAyE,EACnF,iBAAiB,QACb,8CACA,uDACJ;;oDACH;oDACa,0HAAA,CAAA,cAAW,CAAC,MAAM;oDAAC;;;;;;;4CAEhC,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;oDAEC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,WAAW,CAAC,yEAAyE,EACnF,iBAAiB,KAAK,EAAE,GACpB,8CACA,uDACJ;8DAEF,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,IAAI;;;;;;0EAChB,6LAAC;gEAAK,WAAU;;oEAAU;oEAAE,KAAK,KAAK;oEAAC;;;;;;;;;;;;;mDAVpC,KAAK,EAAE;;;;;;;;;;;;;;;;;0CAiBpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,2FAA2F,EACrG,aAAa,OACT,oDACA,uDACJ;;kEAEF,6LAAC,mMAAA,CAAA,MAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,2FAA2F,EACrG,aAAa,OACT,2CACA,uDACJ;;kEAEF,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;4BAKX,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAc,aAAa,IAAI;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;;oDAAgB;oDAAO,aAAa,IAAI;;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;oDAAgB;oDACjB,aAAa,QAAQ,CAAC,CAAC;oDAAC;oDAAG,aAAa,QAAQ,CAAC,CAAC;oDAAC;oDAAG,aAAa,QAAQ,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,aAAa,OAAO,sBAAsB;;;;;;sDAE7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,sMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACZ,aAAa,qBACZ,6LAAC,sMAAA,CAAA,SAAM;wCAAC,QAAQ;4CAAE,UAAU;gDAAC;gDAAG;gDAAG;6CAAE;4CAAE,KAAK;wCAAG;;0DAC7C,6LAAC;;;;;0DACD,6LAAC,oKAAA,CAAA,gBAAa;gDAAC,WAAW;gDAAM,YAAY;gDAAM,cAAc;;;;;;4CAE/D,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAEC,MAAM;oDACN,YAAY,cAAc,OAAO,KAAK,EAAE;oDACxC,SAAS,IAAM,gBAAgB;mDAH1B,KAAK,EAAE;;;;;;;;;;6DAQlB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,6LAAC;oDAAO,WAAU;8DAAuH;;;;;;;;;;;;;;;;;;;;;;8CASjJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAEC,SAAS,IAAM,gBAAgB;oDAC/B,WAAW,CAAC,0DAA0D,EACpE,cAAc,OAAO,KAAK,EAAE,GACxB,8CACA,uDACJ;8DAED,KAAK,IAAI;mDARL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiB1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,gBAAgB;4BAChB,YAAY;wBACd;wBACA,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;IA/MM;MAAA;uCAiNS", "debugId": null}}, {"offset": {"line": 5496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/ClaimAssistantDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { FileText, Check, ArrowRight, Download, Upload, MessageCircle } from 'lucide-react';\n\nconst ClaimAssistantDemo = ({ onComplete, isActive }) => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [claimData, setClaimData] = useState({});\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const claimSteps = [\n    {\n      id: 1,\n      title: \"Select Item\",\n      description: \"Choose the item you want to claim warranty for\",\n      completed: false\n    },\n    {\n      id: 2,\n      title: \"Verify Eligibility\",\n      description: \"Check warranty status and claim eligibility\",\n      completed: false\n    },\n    {\n      id: 3,\n      title: \"Gather Documents\",\n      description: \"Collect required documentation\",\n      completed: false\n    },\n    {\n      id: 4,\n      title: \"Fill Claim Form\",\n      description: \"Complete warranty claim application\",\n      completed: false\n    },\n    {\n      id: 5,\n      title: \"Submit Claim\",\n      description: \"Submit claim to manufacturer\",\n      completed: false\n    }\n  ];\n\n  useEffect(() => {\n    if (isActive) {\n      simulateClaimProcess();\n    }\n  }, [isActive]);\n\n  const simulateClaimProcess = async () => {\n    setIsProcessing(true);\n    \n    for (let i = 0; i < claimSteps.length; i++) {\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setCurrentStep(i + 1);\n      \n      // Update claim data progressively\n      if (i === 0) {\n        setClaimData(prev => ({ ...prev, item: \"iPhone 15 Pro\", serialNumber: \"F2LW48XHQM\" }));\n      } else if (i === 1) {\n        setClaimData(prev => ({ ...prev, eligible: true, warrantyStatus: \"Active\" }));\n      } else if (i === 2) {\n        setClaimData(prev => ({ ...prev, documents: [\"Receipt\", \"Photos\", \"Description\"] }));\n      } else if (i === 3) {\n        setClaimData(prev => ({ ...prev, claimNumber: \"WC-2024-001234\" }));\n      }\n    }\n    \n    setIsProcessing(false);\n    setTimeout(() => onComplete && onComplete(), 2000);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-primary/10 border border-primary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-primary mb-2\">📋 Claim Assistant Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Experience our guided warranty claim process. The AI assistant helps you through \n          every step, ensuring you have the best chance of a successful claim.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-3 gap-6\">\n        \n        {/* Progress Steps */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-semibold text-white\">Claim Progress</h4>\n          \n          <div className=\"space-y-3\">\n            {claimSteps.map((step, index) => (\n              <div\n                key={step.id}\n                className={`flex items-start space-x-3 p-3 rounded-lg border transition-all duration-300 ${\n                  index < currentStep\n                    ? 'border-accent/30 bg-accent/10'\n                    : index === currentStep\n                    ? 'border-primary/30 bg-primary/10'\n                    : 'border-white/20 bg-white/5'\n                }`}\n              >\n                <div className=\"flex-shrink-0 mt-1\">\n                  {index < currentStep ? (\n                    <Check className=\"w-4 h-4 text-accent\" />\n                  ) : index === currentStep ? (\n                    <div className=\"w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin\" />\n                  ) : (\n                    <div className=\"w-4 h-4 border-2 border-white/30 rounded-full\" />\n                  )}\n                </div>\n                <div className=\"flex-1\">\n                  <div className={`font-medium text-sm ${\n                    index < currentStep ? 'text-accent' : \n                    index === currentStep ? 'text-primary' : 'text-white/70'\n                  }`}>\n                    {step.title}\n                  </div>\n                  <div className=\"text-xs text-white/60\">\n                    {step.description}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Current Step Details */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          <h4 className=\"font-semibold text-white\">\n            Step {Math.min(currentStep + 1, claimSteps.length)}: {claimSteps[Math.min(currentStep, claimSteps.length - 1)]?.title}\n          </h4>\n          \n          <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n            {currentStep === 0 && (\n              <div className=\"space-y-4\">\n                <h5 className=\"font-medium text-white\">Select Item for Warranty Claim</h5>\n                <div className=\"grid grid-cols-1 gap-3\">\n                  <div className=\"p-4 border border-primary/30 bg-primary/10 rounded-lg\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <div className=\"font-medium text-white\">iPhone 15 Pro</div>\n                        <div className=\"text-sm text-white/70\">Serial: F2LW48XHQM</div>\n                        <div className=\"text-sm text-white/70\">Warranty: Active (45 days left)</div>\n                      </div>\n                      <div className=\"text-primary\">Selected</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep === 1 && (\n              <div className=\"space-y-4\">\n                <h5 className=\"font-medium text-white\">Warranty Eligibility Check</h5>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-accent/10 border border-accent/20 rounded\">\n                    <span className=\"text-white\">Warranty Status</span>\n                    <span className=\"text-accent font-medium\">✓ Active</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-accent/10 border border-accent/20 rounded\">\n                    <span className=\"text-white\">Purchase Date Verified</span>\n                    <span className=\"text-accent font-medium\">✓ Valid</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-accent/10 border border-accent/20 rounded\">\n                    <span className=\"text-white\">Claim Eligibility</span>\n                    <span className=\"text-accent font-medium\">✓ Eligible</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep === 2 && (\n              <div className=\"space-y-4\">\n                <h5 className=\"font-medium text-white\">Required Documents</h5>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-white/5 border border-white/20 rounded\">\n                    <div className=\"flex items-center space-x-3\">\n                      <FileText className=\"w-4 h-4 text-primary\" />\n                      <span className=\"text-white\">Purchase Receipt</span>\n                    </div>\n                    <Check className=\"w-4 h-4 text-accent\" />\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-white/5 border border-white/20 rounded\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Upload className=\"w-4 h-4 text-primary\" />\n                      <span className=\"text-white\">Product Photos</span>\n                    </div>\n                    <Check className=\"w-4 h-4 text-accent\" />\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-white/5 border border-white/20 rounded\">\n                    <div className=\"flex items-center space-x-3\">\n                      <MessageCircle className=\"w-4 h-4 text-primary\" />\n                      <span className=\"text-white\">Issue Description</span>\n                    </div>\n                    <Check className=\"w-4 h-4 text-accent\" />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep === 3 && (\n              <div className=\"space-y-4\">\n                <h5 className=\"font-medium text-white\">Claim Form Generation</h5>\n                <div className=\"bg-white/5 border border-white/20 rounded-lg p-4\">\n                  <div className=\"space-y-3 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/70\">Claim Number:</span>\n                      <span className=\"text-white font-mono\">WC-2024-001234</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/70\">Product:</span>\n                      <span className=\"text-white\">iPhone 15 Pro</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/70\">Issue Type:</span>\n                      <span className=\"text-white\">Hardware Defect</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-white/70\">Priority:</span>\n                      <span className=\"text-yellow-500\">Medium</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep >= 4 && (\n              <div className=\"space-y-4\">\n                <h5 className=\"font-medium text-white\">Claim Submitted Successfully</h5>\n                <div className=\"text-center py-8\">\n                  <Check className=\"w-16 h-16 text-accent mx-auto mb-4\" />\n                  <div className=\"text-lg font-semibold text-white mb-2\">Claim Submitted!</div>\n                  <div className=\"text-white/70 mb-4\">\n                    Your warranty claim has been submitted to Apple. \n                    You'll receive updates via email and push notifications.\n                  </div>\n                  <div className=\"space-y-2\">\n                    <button className=\"w-full px-4 py-2 bg-primary/20 border border-primary/30 rounded-lg text-primary hover:bg-primary/30 transition-all duration-300\">\n                      <Download className=\"w-4 h-4 inline mr-2\" />\n                      Download Claim Receipt\n                    </button>\n                    <button className=\"w-full px-4 py-2 bg-secondary/20 border border-secondary/30 rounded-lg text-secondary hover:bg-secondary/30 transition-all duration-300\">\n                      Track Claim Status\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* AI Assistant Chat */}\n          <div className=\"bg-white/5 border border-white/10 rounded-lg p-4\">\n            <h5 className=\"font-medium text-white mb-3\">AI Assistant</h5>\n            <div className=\"space-y-3 max-h-32 overflow-y-auto\">\n              <div className=\"flex space-x-3\">\n                <div className=\"w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs\">AI</div>\n                <div className=\"flex-1 bg-primary/10 rounded-lg p-2\">\n                  <div className=\"text-sm text-white\">\n                    {currentStep === 0 && \"I've found your iPhone 15 Pro. It's still under warranty. Would you like to proceed with the claim?\"}\n                    {currentStep === 1 && \"Great! Your device is eligible for warranty service. Let me gather the required documents.\"}\n                    {currentStep === 2 && \"I've found all your documents in our system. Everything looks good!\"}\n                    {currentStep === 3 && \"I'm generating your claim form with all the details. This will only take a moment.\"}\n                    {currentStep >= 4 && \"Perfect! Your claim has been submitted successfully. Apple typically responds within 2-3 business days.\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <button\n          onClick={() => {\n            setCurrentStep(0);\n            setClaimData({});\n            setIsProcessing(false);\n          }}\n          className=\"px-4 py-2 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n        >\n          Start New Claim\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          {currentStep >= claimSteps.length && (\n            <div className=\"flex items-center space-x-2 text-accent\">\n              <Check className=\"w-4 h-4\" />\n              <span className=\"text-sm\">Claim Process Complete!</span>\n            </div>\n          )}\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            disabled={currentStep < claimSteps.length}\n            className=\"px-6 py-2 bg-gradient-to-r from-primary to-accent rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:shadow-primary/25 transition-all duration-300\"\n          >\n            Continue to Full Workflow\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ClaimAssistantDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,qBAAqB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;uCAAG;QAAC;KAAS;IAEb,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe,IAAI;YAEnB,kCAAkC;YAClC,IAAI,MAAM,GAAG;gBACX,aAAa,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;wBAAiB,cAAc;oBAAa,CAAC;YACtF,OAAO,IAAI,MAAM,GAAG;gBAClB,aAAa,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;wBAAM,gBAAgB;oBAAS,CAAC;YAC7E,OAAO,IAAI,MAAM,GAAG;gBAClB,aAAa,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;4BAAC;4BAAW;4BAAU;yBAAc;oBAAC,CAAC;YACpF,OAAO,IAAI,MAAM,GAAG;gBAClB,aAAa,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAiB,CAAC;YAClE;QACF;QAEA,gBAAgB;QAChB,WAAW,IAAM,cAAc,cAAc;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wCAEC,WAAW,CAAC,6EAA6E,EACvF,QAAQ,cACJ,kCACA,UAAU,cACV,oCACA,8BACJ;;0DAEF,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,4BACP,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;2DACf,UAAU,4BACZ,6LAAC;oDAAI,WAAU;;;;;yEAEf,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,oBAAoB,EACnC,QAAQ,cAAc,gBACtB,UAAU,cAAc,iBAAiB,iBACzC;kEACC,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;;;;;;;uCA1BhB,KAAK,EAAE;;;;;;;;;;;;;;;;kCAmCpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA2B;oCACjC,KAAK,GAAG,CAAC,cAAc,GAAG,WAAW,MAAM;oCAAE;oCAAG,UAAU,CAAC,KAAK,GAAG,CAAC,aAAa,WAAW,MAAM,GAAG,GAAG,EAAE;;;;;;;0CAGlH,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;0EAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOvC,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,6LAAC;gEAAK,WAAU;0EAA0B;;;;;;;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,6LAAC;gEAAK,WAAU;0EAA0B;;;;;;;;;;;;kEAE5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,6LAAC;gEAAK,WAAU;0EAA0B;;;;;;;;;;;;;;;;;;;;;;;;oCAMjD,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAa;;;;;;;;;;;;0EAE/B,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;wEAAK,WAAU;kFAAa;;;;;;;;;;;;0EAE/B,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,2NAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;kFACzB,6LAAC;wEAAK,WAAU;kFAAa;;;;;;;;;;;;0EAE/B,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;oCAMxB,gBAAgB,mBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAa;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAa;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAO3C,eAAe,mBACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEAAqB;;;;;;kEAIpC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAwB;;;;;;;0EAG9C,6LAAC;gEAAO,WAAU;0EAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUtK,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA2E;;;;;;8DAC1F,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,gBAAgB,KAAK;4DACrB,gBAAgB,KAAK;4DACrB,gBAAgB,KAAK;4DACrB,gBAAgB,KAAK;4DACrB,eAAe,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,eAAe;4BACf,aAAa,CAAC;4BACd,gBAAgB;wBAClB;wBACA,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,WAAW,MAAM,kBAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU,cAAc,WAAW,MAAM;gCACzC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxSM;KAAA;uCA0SS", "debugId": null}}, {"offset": {"line": 6437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/demo/FullWorkflowDemo.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Play, Pause, RotateCcw, Check, ArrowRight, Zap } from 'lucide-react';\n\nconst FullWorkflowDemo = ({ onComplete, isActive }) => {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentPhase, setCurrentPhase] = useState(0);\n  const [progress, setProgress] = useState(0);\n\n  const workflowPhases = [\n    {\n      id: 1,\n      title: \"Receipt Upload\",\n      description: \"User uploads iPhone receipt photo\",\n      duration: 3000,\n      steps: [\"Photo captured\", \"Image processed\", \"OCR completed\"]\n    },\n    {\n      id: 2,\n      title: \"AI Analysis\",\n      description: \"AI extracts warranty information\",\n      duration: 2500,\n      steps: [\"Product identified\", \"Warranty terms extracted\", \"Data validated\"]\n    },\n    {\n      id: 3,\n      title: \"Dashboard Update\",\n      description: \"Item added to warranty dashboard\",\n      duration: 2000,\n      steps: [\"Database updated\", \"Reminders set\", \"Categories organized\"]\n    },\n    {\n      id: 4,\n      title: \"Smart Reminders\",\n      description: \"Proactive notifications configured\",\n      duration: 2000,\n      steps: [\"Calendar integration\", \"Email alerts set\", \"Push notifications enabled\"]\n    },\n    {\n      id: 5,\n      title: \"3D Visualization\",\n      description: \"Item appears in 3D inventory\",\n      duration: 2500,\n      steps: [\"3D model generated\", \"Room placement\", \"AR preview ready\"]\n    },\n    {\n      id: 6,\n      title: \"Warranty Claim\",\n      description: \"Seamless claim process when needed\",\n      duration: 3000,\n      steps: [\"Eligibility verified\", \"Documents prepared\", \"Claim submitted\"]\n    }\n  ];\n\n  useEffect(() => {\n    if (isActive && !isPlaying) {\n      startWorkflow();\n    }\n  }, [isActive]);\n\n  const startWorkflow = async () => {\n    setIsPlaying(true);\n    setCurrentPhase(0);\n    setProgress(0);\n\n    for (let i = 0; i < workflowPhases.length; i++) {\n      setCurrentPhase(i);\n      \n      // Animate progress for current phase\n      const phase = workflowPhases[i];\n      const stepDuration = phase.duration / phase.steps.length;\n      \n      for (let step = 0; step < phase.steps.length; step++) {\n        await new Promise(resolve => setTimeout(resolve, stepDuration));\n        setProgress((step + 1) / phase.steps.length * 100);\n      }\n      \n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n\n    setIsPlaying(false);\n    setTimeout(() => onComplete && onComplete(), 2000);\n  };\n\n  const resetWorkflow = () => {\n    setIsPlaying(false);\n    setCurrentPhase(0);\n    setProgress(0);\n  };\n\n  const togglePlayback = () => {\n    if (isPlaying) {\n      setIsPlaying(false);\n    } else {\n      startWorkflow();\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border border-primary/20 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-primary mb-2\">🚀 Complete Workflow Demo</h3>\n        <p className=\"text-white/80 text-sm\">\n          Experience the full WarrantyAI journey from receipt upload to warranty claim. \n          This comprehensive demo shows how all features work together seamlessly.\n        </p>\n      </div>\n\n      {/* Workflow Controls */}\n      <div className=\"flex items-center justify-center space-x-4\">\n        <button\n          onClick={togglePlayback}\n          disabled={isPlaying}\n          className=\"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg text-white hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 disabled:opacity-50\"\n        >\n          {isPlaying ? <Pause className=\"w-5 h-5\" /> : <Play className=\"w-5 h-5\" />}\n          <span>{isPlaying ? 'Playing...' : 'Start Demo'}</span>\n        </button>\n        \n        <button\n          onClick={resetWorkflow}\n          className=\"flex items-center space-x-2 px-4 py-3 border border-white/20 rounded-lg text-white/70 hover:text-white hover:border-white/40 transition-all duration-300\"\n        >\n          <RotateCcw className=\"w-4 h-4\" />\n          <span>Reset</span>\n        </button>\n      </div>\n\n      {/* Workflow Visualization */}\n      <div className=\"space-y-6\">\n        \n        {/* Timeline */}\n        <div className=\"relative\">\n          <div className=\"flex items-center justify-between\">\n            {workflowPhases.map((phase, index) => (\n              <div key={phase.id} className=\"flex flex-col items-center relative\">\n                {/* Phase Circle */}\n                <div className={`w-12 h-12 rounded-full border-4 flex items-center justify-center transition-all duration-500 ${\n                  index < currentPhase \n                    ? 'border-accent bg-accent text-space' \n                    : index === currentPhase\n                    ? 'border-primary bg-primary text-space animate-pulse'\n                    : 'border-white/30 bg-white/10 text-white/60'\n                }`}>\n                  {index < currentPhase ? (\n                    <Check className=\"w-5 h-5\" />\n                  ) : index === currentPhase ? (\n                    <Zap className=\"w-5 h-5\" />\n                  ) : (\n                    <span className=\"text-sm font-bold\">{index + 1}</span>\n                  )}\n                </div>\n                \n                {/* Phase Label */}\n                <div className=\"mt-2 text-center\">\n                  <div className={`text-sm font-medium ${\n                    index <= currentPhase ? 'text-white' : 'text-white/60'\n                  }`}>\n                    {phase.title}\n                  </div>\n                </div>\n\n                {/* Connector Line */}\n                {index < workflowPhases.length - 1 && (\n                  <div className={`absolute top-6 left-12 w-24 h-1 transition-all duration-500 ${\n                    index < currentPhase ? 'bg-accent' : 'bg-white/20'\n                  }`}></div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Current Phase Details */}\n        <div className=\"bg-white/5 border border-white/10 rounded-lg p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h4 className=\"font-semibold text-white text-lg\">\n              {workflowPhases[currentPhase]?.title}\n            </h4>\n            <div className=\"text-sm text-white/60\">\n              Phase {currentPhase + 1} of {workflowPhases.length}\n            </div>\n          </div>\n          \n          <p className=\"text-white/80 mb-4\">\n            {workflowPhases[currentPhase]?.description}\n          </p>\n\n          {/* Progress Bar */}\n          <div className=\"mb-4\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <span className=\"text-sm text-white/70\">Progress</span>\n              <span className=\"text-sm text-white/70\">{Math.round(progress)}%</span>\n            </div>\n            <div className=\"w-full bg-white/20 rounded-full h-2\">\n              <div \n                className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${progress}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Current Steps */}\n          <div className=\"space-y-2\">\n            {workflowPhases[currentPhase]?.steps.map((step, stepIndex) => {\n              const stepProgress = (progress / 100) * workflowPhases[currentPhase].steps.length;\n              const isStepComplete = stepIndex < stepProgress;\n              const isStepActive = stepIndex < stepProgress + 1 && stepIndex >= stepProgress - 1;\n\n              return (\n                <div key={stepIndex} className={`flex items-center space-x-3 p-2 rounded transition-all duration-300 ${\n                  isStepComplete ? 'bg-accent/10' : isStepActive ? 'bg-primary/10' : 'bg-white/5'\n                }`}>\n                  <div className={`w-4 h-4 rounded-full flex items-center justify-center ${\n                    isStepComplete ? 'bg-accent' : isStepActive ? 'bg-primary' : 'bg-white/20'\n                  }`}>\n                    {isStepComplete ? (\n                      <Check className=\"w-3 h-3 text-space\" />\n                    ) : isStepActive ? (\n                      <div className=\"w-2 h-2 bg-space rounded-full animate-pulse\" />\n                    ) : (\n                      <div className=\"w-2 h-2 bg-white/40 rounded-full\" />\n                    )}\n                  </div>\n                  <span className={`text-sm ${\n                    isStepComplete ? 'text-accent' : isStepActive ? 'text-primary' : 'text-white/60'\n                  }`}>\n                    {step}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Feature Highlights */}\n        <div className=\"grid md:grid-cols-3 gap-4\">\n          <div className=\"bg-primary/10 border border-primary/20 rounded-lg p-4\">\n            <h5 className=\"font-semibold text-primary mb-2\">AI-Powered</h5>\n            <p className=\"text-white/70 text-sm\">\n              Advanced machine learning extracts warranty information with 99.9% accuracy\n            </p>\n          </div>\n          \n          <div className=\"bg-secondary/10 border border-secondary/20 rounded-lg p-4\">\n            <h5 className=\"font-semibold text-secondary mb-2\">Fully Automated</h5>\n            <p className=\"text-white/70 text-sm\">\n              From upload to claim, everything happens automatically in the background\n            </p>\n          </div>\n          \n          <div className=\"bg-accent/10 border border-accent/20 rounded-lg p-4\">\n            <h5 className=\"font-semibold text-accent mb-2\">Always Connected</h5>\n            <p className=\"text-white/70 text-sm\">\n              Real-time sync across all devices with cloud backup and security\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between pt-4 border-t border-white/10\">\n        <div className=\"text-white/70 text-sm\">\n          Complete workflow demonstration • All features integrated\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          {currentPhase >= workflowPhases.length - 1 && !isPlaying && (\n            <div className=\"flex items-center space-x-2 text-accent\">\n              <Check className=\"w-4 h-4\" />\n              <span className=\"text-sm\">All Demos Complete!</span>\n            </div>\n          )}\n          \n          <button\n            onClick={() => onComplete && onComplete()}\n            disabled={isPlaying || currentPhase < workflowPhases.length - 1}\n            className=\"px-6 py-2 bg-gradient-to-r from-accent to-secondary rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:shadow-accent/25 transition-all duration-300\"\n          >\n            Complete Demo Experience\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FullWorkflowDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,mBAAmB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE;;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAkB;gBAAmB;aAAgB;QAC/D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAsB;gBAA4B;aAAiB;QAC7E;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAoB;gBAAiB;aAAuB;QACtE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAwB;gBAAoB;aAA6B;QACnF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAsB;gBAAkB;aAAmB;QACrE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,OAAO;gBAAC;gBAAwB;gBAAsB;aAAkB;QAC1E;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY,CAAC,WAAW;gBAC1B;YACF;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,aAAa;QACb,gBAAgB;QAChB,YAAY;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,gBAAgB;YAEhB,qCAAqC;YACrC,MAAM,QAAQ,cAAc,CAAC,EAAE;YAC/B,MAAM,eAAe,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM;YAExD,IAAK,IAAI,OAAO,GAAG,OAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAQ;gBACpD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,YAAY,CAAC,OAAO,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG;YAChD;YAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,aAAa;QACb,WAAW,IAAM,cAAc,cAAc;IAC/C;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW;YACb,aAAa;QACf,OAAO;YACL;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;;4BAET,0BAAY,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAC7D,6LAAC;0CAAM,YAAY,eAAe;;;;;;;;;;;;kCAGpC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;oCAAmB,WAAU;;sDAE5B,6LAAC;4CAAI,WAAW,CAAC,6FAA6F,EAC5G,QAAQ,eACJ,uCACA,UAAU,eACV,uDACA,6CACJ;sDACC,QAAQ,6BACP,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uDACf,UAAU,6BACZ,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;qEAEf,6LAAC;gDAAK,WAAU;0DAAqB,QAAQ;;;;;;;;;;;sDAKjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,SAAS,eAAe,eAAe,iBACvC;0DACC,MAAM,KAAK;;;;;;;;;;;wCAKf,QAAQ,eAAe,MAAM,GAAG,mBAC/B,6LAAC;4CAAI,WAAW,CAAC,4DAA4D,EAC3E,QAAQ,eAAe,cAAc,eACrC;;;;;;;mCA/BI,MAAM,EAAE;;;;;;;;;;;;;;;kCAuCxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,cAAc,CAAC,aAAa,EAAE;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;4CAAwB;4CAC9B,eAAe;4CAAE;4CAAK,eAAe,MAAM;;;;;;;;;;;;;0CAItD,6LAAC;gCAAE,WAAU;0CACV,cAAc,CAAC,aAAa,EAAE;;;;;;0CAIjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;;oDAAyB,KAAK,KAAK,CAAC;oDAAU;;;;;;;;;;;;;kDAEhE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;0CAMrC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM;oCAC9C,MAAM,eAAe,AAAC,WAAW,MAAO,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM;oCACjF,MAAM,iBAAiB,YAAY;oCACnC,MAAM,eAAe,YAAY,eAAe,KAAK,aAAa,eAAe;oCAEjF,qBACE,6LAAC;wCAAoB,WAAW,CAAC,oEAAoE,EACnG,iBAAiB,iBAAiB,eAAe,kBAAkB,cACnE;;0DACA,6LAAC;gDAAI,WAAW,CAAC,sDAAsD,EACrE,iBAAiB,cAAc,eAAe,eAAe,eAC7D;0DACC,+BACC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;2DACf,6BACF,6LAAC;oDAAI,WAAU;;;;;yEAEf,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAGnB,6LAAC;gDAAK,WAAW,CAAC,QAAQ,EACxB,iBAAiB,gBAAgB,eAAe,iBAAiB,iBACjE;0DACC;;;;;;;uCAjBK;;;;;gCAqBd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,eAAe,MAAM,GAAG,KAAK,CAAC,2BAC7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI9B,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,UAAU,aAAa,eAAe,eAAe,MAAM,GAAG;gCAC9D,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxRM;KAAA;uCA0RS", "debugId": null}}, {"offset": {"line": 7058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/app/demo/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Navigation from '@/components/ui/Navigation';\nimport Footer from '@/components/ui/Footer';\nimport DemoLevelSelector from '@/components/demo/DemoLevelSelector';\nimport ReceiptUploadDemo from '@/components/demo/ReceiptUploadDemo';\nimport AIExtractionDemo from '@/components/demo/AIExtractionDemo';\nimport DashboardDemo from '@/components/demo/DashboardDemo';\nimport ReminderSystemDemo from '@/components/demo/ReminderSystemDemo';\nimport Inventory3DDemo from '@/components/demo/Inventory3DDemo';\nimport ClaimAssistantDemo from '@/components/demo/ClaimAssistantDemo';\nimport FullWorkflowDemo from '@/components/demo/FullWorkflowDemo';\n\nexport default function DemoPage() {\n  const [currentLevel, setCurrentLevel] = useState(1);\n  const [completedLevels, setCompletedLevels] = useState([]);\n\n  const demoLevels = [\n    {\n      id: 1,\n      title: \"Receipt Upload\",\n      description: \"Experience our AI-powered receipt scanning\",\n      component: ReceiptUploadDemo,\n      difficulty: \"Beginner\",\n      duration: \"2 min\"\n    },\n    {\n      id: 2,\n      title: \"AI Extraction\",\n      description: \"Watch AI extract warranty information\",\n      component: AIExtractionDemo,\n      difficulty: \"Beginner\",\n      duration: \"3 min\"\n    },\n    {\n      id: 3,\n      title: \"Dashboard Navigation\",\n      description: \"Explore the warranty management dashboard\",\n      component: DashboardDemo,\n      difficulty: \"Intermediate\",\n      duration: \"5 min\"\n    },\n    {\n      id: 4,\n      title: \"Smart Reminders\",\n      description: \"See how intelligent notifications work\",\n      component: ReminderSystemDemo,\n      difficulty: \"Intermediate\",\n      duration: \"4 min\"\n    },\n    {\n      id: 5,\n      title: \"3D Inventory\",\n      description: \"Visualize your items in 3D space\",\n      component: Inventory3DDemo,\n      difficulty: \"Advanced\",\n      duration: \"6 min\"\n    },\n    {\n      id: 6,\n      title: \"Claim Assistant\",\n      description: \"Get guided help with warranty claims\",\n      component: ClaimAssistantDemo,\n      difficulty: \"Advanced\",\n      duration: \"7 min\"\n    },\n    {\n      id: 7,\n      title: \"Full Workflow\",\n      description: \"Complete end-to-end warranty management\",\n      component: FullWorkflowDemo,\n      difficulty: \"Expert\",\n      duration: \"10 min\"\n    }\n  ];\n\n  const handleLevelComplete = (levelId) => {\n    if (!completedLevels.includes(levelId)) {\n      setCompletedLevels([...completedLevels, levelId]);\n    }\n    \n    // Auto-advance to next level\n    if (levelId < demoLevels.length) {\n      setTimeout(() => {\n        setCurrentLevel(levelId + 1);\n      }, 1500);\n    }\n  };\n\n  const handleLevelSelect = (levelId) => {\n    setCurrentLevel(levelId);\n  };\n\n  const currentLevelData = demoLevels.find(level => level.id === currentLevel);\n  const CurrentComponent = currentLevelData?.component;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-space via-surface to-space\">\n      <Navigation />\n      \n      {/* Demo Header */}\n      <section className=\"pt-24 pb-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h1 className=\"font-heading text-4xl md:text-6xl font-bold mb-6\">\n            <span className=\"text-white\">Interactive</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Demo Experience\n            </span>\n          </h1>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto mb-8\">\n            Explore WarrantyAI's powerful features through hands-on demonstrations. \n            Each level showcases different aspects of our AI-powered warranty management system.\n          </p>\n          \n          {/* Progress Bar */}\n          <div className=\"max-w-2xl mx-auto mb-8\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <span className=\"text-sm text-white/60\">Progress</span>\n              <span className=\"text-sm text-white/60\">\n                {completedLevels.length} / {demoLevels.length} completed\n              </span>\n            </div>\n            <div className=\"w-full bg-surface/50 rounded-full h-2\">\n              <div \n                className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-500\"\n                style={{ width: `${(completedLevels.length / demoLevels.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Demo Content */}\n      <section className=\"pb-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-4 gap-8\">\n            \n            {/* Level Selector Sidebar */}\n            <div className=\"lg:col-span-1\">\n              <DemoLevelSelector\n                levels={demoLevels}\n                currentLevel={currentLevel}\n                completedLevels={completedLevels}\n                onLevelSelect={handleLevelSelect}\n              />\n            </div>\n\n            {/* Demo Area */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"glass rounded-lg border border-white/10 overflow-hidden\">\n                \n                {/* Demo Header */}\n                <div className=\"bg-surface/50 border-b border-white/10 p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h2 className=\"font-heading text-2xl font-bold text-white mb-2\">\n                        Level {currentLevel}: {currentLevelData?.title}\n                      </h2>\n                      <p className=\"text-white/70\">\n                        {currentLevelData?.description}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"flex items-center space-x-4 text-sm text-white/60\">\n                        <span className={`px-3 py-1 rounded-full border ${\n                          currentLevelData?.difficulty === 'Beginner' ? 'border-accent/30 text-accent' :\n                          currentLevelData?.difficulty === 'Intermediate' ? 'border-primary/30 text-primary' :\n                          currentLevelData?.difficulty === 'Advanced' ? 'border-secondary/30 text-secondary' :\n                          'border-white/30 text-white'\n                        }`}>\n                          {currentLevelData?.difficulty}\n                        </span>\n                        <span>⏱️ {currentLevelData?.duration}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Demo Component */}\n                <div className=\"p-6\">\n                  {CurrentComponent && (\n                    <CurrentComponent\n                      onComplete={() => handleLevelComplete(currentLevel)}\n                      isActive={true}\n                    />\n                  )}\n                </div>\n              </div>\n\n              {/* Demo Controls */}\n              <div className=\"flex items-center justify-between mt-6\">\n                <button\n                  onClick={() => currentLevel > 1 && setCurrentLevel(currentLevel - 1)}\n                  disabled={currentLevel === 1}\n                  className=\"px-6 py-3 border border-white/20 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/5 transition-all duration-300\"\n                >\n                  ← Previous Level\n                </button>\n                \n                <div className=\"flex items-center space-x-2\">\n                  {demoLevels.map((level) => (\n                    <button\n                      key={level.id}\n                      onClick={() => setCurrentLevel(level.id)}\n                      className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                        level.id === currentLevel\n                          ? 'bg-primary scale-125'\n                          : completedLevels.includes(level.id)\n                          ? 'bg-accent'\n                          : 'bg-white/20 hover:bg-white/40'\n                      }`}\n                    />\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => currentLevel < demoLevels.length && setCurrentLevel(currentLevel + 1)}\n                  disabled={currentLevel === demoLevels.length}\n                  className=\"px-6 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:shadow-primary/25 transition-all duration-300\"\n                >\n                  Next Level →\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEzD,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,iJAAA,CAAA,UAAiB;YAC5B,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,gJAAA,CAAA,UAAgB;YAC3B,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,6IAAA,CAAA,UAAa;YACxB,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,kJAAA,CAAA,UAAkB;YAC7B,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,+IAAA,CAAA,UAAe;YAC1B,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,kJAAA,CAAA,UAAkB;YAC7B,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW,gJAAA,CAAA,UAAgB;YAC3B,YAAY;YACZ,UAAU;QACZ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,gBAAgB,QAAQ,CAAC,UAAU;YACtC,mBAAmB;mBAAI;gBAAiB;aAAQ;QAClD;QAEA,6BAA6B;QAC7B,IAAI,UAAU,WAAW,MAAM,EAAE;YAC/B,WAAW;gBACT,gBAAgB,UAAU;YAC5B,GAAG;QACL;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC/D,MAAM,mBAAmB,kBAAkB;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;;sCAIxG,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAM5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,6LAAC;4CAAK,WAAU;;gDACb,gBAAgB,MAAM;gDAAC;gDAAI,WAAW,MAAM;gDAAC;;;;;;;;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,AAAC,gBAAgB,MAAM,GAAG,WAAW,MAAM,GAAI,IAAI,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnF,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iJAAA,CAAA,UAAiB;oCAChB,QAAQ;oCACR,cAAc;oCACd,iBAAiB;oCACjB,eAAe;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAGb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;;wEAAkD;wEACvD;wEAAa;wEAAG,kBAAkB;;;;;;;8EAE3C,6LAAC;oEAAE,WAAU;8EACV,kBAAkB;;;;;;;;;;;;sEAGvB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,CAAC,8BAA8B,EAC9C,kBAAkB,eAAe,aAAa,iCAC9C,kBAAkB,eAAe,iBAAiB,mCAClD,kBAAkB,eAAe,aAAa,uCAC9C,8BACA;kFACC,kBAAkB;;;;;;kFAErB,6LAAC;;4EAAK;4EAAI,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOpC,6LAAC;gDAAI,WAAU;0DACZ,kCACC,6LAAC;oDACC,YAAY,IAAM,oBAAoB;oDACtC,UAAU;;;;;;;;;;;;;;;;;kDAOlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe,KAAK,gBAAgB,eAAe;gDAClE,UAAU,iBAAiB;gDAC3B,WAAU;0DACX;;;;;;0DAID,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC;wDAEC,SAAS,IAAM,gBAAgB,MAAM,EAAE;wDACvC,WAAW,CAAC,iDAAiD,EAC3D,MAAM,EAAE,KAAK,eACT,yBACA,gBAAgB,QAAQ,CAAC,MAAM,EAAE,IACjC,cACA,iCACJ;uDARG,MAAM,EAAE;;;;;;;;;;0DAanB,6LAAC;gDACC,SAAS,IAAM,eAAe,WAAW,MAAM,IAAI,gBAAgB,eAAe;gDAClF,UAAU,iBAAiB,WAAW,MAAM;gDAC5C,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,oIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA3NwB;KAAA", "debugId": null}}]}