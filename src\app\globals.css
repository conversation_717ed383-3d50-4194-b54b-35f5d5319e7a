@import "tailwindcss";

/* WarrantyAI Futuristic Design System */
:root {
  /* Color Palette */
  --color-primary: #00D4FF;        /* Electric Blue */
  --color-secondary: #8B5CF6;      /* Neon Purple */
  --color-accent: #00FF88;         /* Cyber Green */
  --color-space: #0A0A0F;          /* Deep Space */
  --color-surface: #1A1A2E;        /* Dark Glass */
  --color-white: #FFFFFF;          /* Pure White */
  --color-muted: #64748B;          /* Steel Gray */

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  --gradient-accent: linear-gradient(135deg, var(--color-accent), var(--color-primary));
  --gradient-surface: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(10, 10, 15, 0.9));

  /* Shadows & Glows */
  --glow-primary: 0 0 20px rgba(0, 212, 255, 0.3);
  --glow-secondary: 0 0 20px rgba(139, 92, 246, 0.3);
  --glow-accent: 0 0 20px rgba(0, 255, 136, 0.3);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Animations */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@theme inline {
  /* Colors */
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-accent: var(--color-accent);
  --color-space: var(--color-space);
  --color-surface: var(--color-surface);
  --color-white: var(--color-white);
  --color-muted: var(--color-muted);

  /* Fonts */
  --font-sans: var(--font-inter);
  --font-heading: var(--font-orbitron);
  --font-mono: var(--font-jetbrains-mono);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter), system-ui, sans-serif;
  background: var(--color-space);
  color: var(--color-white);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-space);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Glassmorphism Effect */
.glass {
  background: rgba(26, 26, 46, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-glass);
}

/* Glow Effects */
.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

/* Text Glow */
.text-glow-primary {
  text-shadow: 0 0 10px var(--color-primary);
}

.text-glow-accent {
  text-shadow: 0 0 10px var(--color-accent);
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px var(--color-primary); }
  50% { box-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary); }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100vh); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-matrix-rain {
  animation: matrix-rain 3s linear infinite;
}

/* Utility Classes */
.transition-smooth {
  transition: var(--transition-smooth);
}

.transition-bounce {
  transition: var(--transition-bounce);
}

/* Focus States */
.focus-glow:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary);
}

/* Selection */
::selection {
  background: var(--color-primary);
  color: var(--color-space);
}
