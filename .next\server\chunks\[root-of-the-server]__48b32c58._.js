module.exports = {

"[project]/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/favicon--route-entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-rsc] (ecmascript)");
;
const contentType = "image/x-icon";
const cacheControl = "public, max-age=0, must-revalidate";
const buffer = Buffer.from("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", 'base64');
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
function GET() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': cacheControl
        }
    });
}
const dynamic = 'force-static';
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__48b32c58._.js.map