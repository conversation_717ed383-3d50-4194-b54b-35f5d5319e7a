{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n      scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Zap className=\"w-8 h-8 text-primary group-hover:text-accent transition-colors duration-300\" />\n              <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md group-hover:bg-accent/20 transition-colors duration-300\"></div>\n            </div>\n            <span className=\"font-heading text-xl font-bold text-white group-hover:text-glow-primary transition-all duration-300\">\n              WarrantyAI\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white/80 hover:text-primary transition-colors duration-300 font-medium relative group\"\n              >\n                {item.label}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-accent transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-primary transition-colors duration-300 p-2\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 glass backdrop-blur-md rounded-lg mt-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white/80 hover:text-primary transition-colors duration-300 font-medium\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/signup\"\n                className=\"block w-full text-center bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-full text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAC3E,WAAW,2BAA2B,kBACtC;kBACA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAAsG;;;;;;;;;;;;sCAMxH,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;0DACX,8OAAC;gDAAK,WAAU;;;;;;;uCALX,KAAK,IAAI;;;;;8CAQlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/three/AIParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useMemo } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Particle system component\nfunction ParticleField({ count = 1000 }) {\n  const mesh = useRef();\n  const light = useRef();\n\n  // Generate random positions for particles\n  const particles = useMemo(() => {\n    const temp = new Float32Array(count * 3);\n    for (let i = 0; i < count; i++) {\n      const i3 = i * 3;\n      temp[i3] = (Math.random() - 0.5) * 20;\n      temp[i3 + 1] = (Math.random() - 0.5) * 20;\n      temp[i3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return temp;\n  }, [count]);\n\n  // Animation loop\n  useFrame((state) => {\n    const time = state.clock.getElapsedTime();\n    \n    if (mesh.current) {\n      mesh.current.rotation.x = time * 0.1;\n      mesh.current.rotation.y = time * 0.05;\n      \n      // Update particle positions for floating effect\n      const positions = mesh.current.geometry.attributes.position.array;\n      for (let i = 0; i < positions.length; i += 3) {\n        positions[i + 1] += Math.sin(time + positions[i]) * 0.001;\n      }\n      mesh.current.geometry.attributes.position.needsUpdate = true;\n    }\n\n    if (light.current) {\n      light.current.position.x = Math.sin(time) * 5;\n      light.current.position.z = Math.cos(time) * 5;\n    }\n  });\n\n  return (\n    <group>\n      <Points ref={mesh} positions={particles} stride={3} frustumCulled={false}>\n        <PointMaterial\n          transparent\n          color=\"#00D4FF\"\n          size={0.05}\n          sizeAttenuation={true}\n          depthWrite={false}\n          blending={THREE.AdditiveBlending}\n        />\n      </Points>\n      \n      {/* AI Network Lines */}\n      <mesh>\n        <sphereGeometry args={[8, 32, 32]} />\n        <meshBasicMaterial\n          color=\"#8B5CF6\"\n          transparent\n          opacity={0.1}\n          wireframe\n        />\n      </mesh>\n\n      {/* Floating light */}\n      <pointLight\n        ref={light}\n        color=\"#00FF88\"\n        intensity={0.5}\n        distance={10}\n        decay={2}\n      />\n    </group>\n  );\n}\n\n// Neural network connections\nfunction NeuralNetwork() {\n  const lines = useRef();\n\n  useFrame((state) => {\n    if (lines.current) {\n      lines.current.rotation.y = state.clock.getElapsedTime() * 0.1;\n    }\n  });\n\n  const connections = useMemo(() => {\n    const points = [];\n    const nodeCount = 20;\n    \n    // Create nodes in 3D space\n    const nodes = [];\n    for (let i = 0; i < nodeCount; i++) {\n      nodes.push(new THREE.Vector3(\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15\n      ));\n    }\n\n    // Connect nearby nodes\n    for (let i = 0; i < nodes.length; i++) {\n      for (let j = i + 1; j < nodes.length; j++) {\n        if (nodes[i].distanceTo(nodes[j]) < 5) {\n          points.push(nodes[i], nodes[j]);\n        }\n      }\n    }\n\n    return points;\n  }, []);\n\n  return (\n    <group ref={lines}>\n      <lineSegments>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={connections.length}\n            array={new Float32Array(connections.flatMap(v => [v.x, v.y, v.z]))}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <lineBasicMaterial\n          color=\"#00D4FF\"\n          transparent\n          opacity={0.3}\n          blending={THREE.AdditiveBlending}\n        />\n      </lineSegments>\n    </group>\n  );\n}\n\n// Main background component\nconst AIParticleBackground = () => {\n  return (\n    <div className=\"absolute inset-0 w-full h-full\">\n      <Canvas\n        camera={{ position: [0, 0, 10], fov: 60 }}\n        style={{ background: 'transparent' }}\n      >\n        <ambientLight intensity={0.2} />\n        <ParticleField count={1500} />\n        <NeuralNetwork />\n        \n        {/* Fog for depth */}\n        <fog attach=\"fog\" args={['#0A0A0F', 10, 50]} />\n      </Canvas>\n    </div>\n  );\n};\n\nexport default AIParticleBackground;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAOA,4BAA4B;AAC5B,SAAS,cAAc,EAAE,QAAQ,IAAI,EAAE;IACrC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAClB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEnB,0CAA0C;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,OAAO,IAAI,aAAa,QAAQ;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,KAAK,IAAI;YACf,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACnC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QACzC;QACA,OAAO;IACT,GAAG;QAAC;KAAM;IAEV,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,MAAM,OAAO,MAAM,KAAK,CAAC,cAAc;QAEvC,IAAI,KAAK,OAAO,EAAE;YAChB,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;YACjC,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO;YAEjC,gDAAgD;YAChD,MAAM,YAAY,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK;YACjE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;gBAC5C,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,SAAS,CAAC,EAAE,IAAI;YACtD;YACA,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;QAC1D;QAEA,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;YAC5C,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;QAC9C;IACF;IAEA,qBACE,8OAAC;;0BACC,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAM,WAAW;gBAAW,QAAQ;gBAAG,eAAe;0BACjE,cAAA,8OAAC,iKAAA,CAAA,gBAAa;oBACZ,WAAW;oBACX,OAAM;oBACN,MAAM;oBACN,iBAAiB;oBACjB,YAAY;oBACZ,UAAU,+IAAA,CAAA,mBAAsB;;;;;;;;;;;0BAKpC,8OAAC;;kCACC,8OAAC;wBAAe,MAAM;4BAAC;4BAAG;4BAAI;yBAAG;;;;;;kCACjC,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,SAAS;;;;;;;;;;;;0BAKb,8OAAC;gBACC,KAAK;gBACL,OAAM;gBACN,WAAW;gBACX,UAAU;gBACV,OAAO;;;;;;;;;;;;AAIf;AAEA,6BAA6B;AAC7B,SAAS;IACP,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEnB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,cAAc,KAAK;QAC5D;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,SAAS,EAAE;QACjB,MAAM,YAAY;QAElB,2BAA2B;QAC3B,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,MAAM,IAAI,CAAC,IAAI,+IAAA,CAAA,UAAa,CAC1B,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAE5B;QAEA,uBAAuB;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACzC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG;oBACrC,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;gBAChC;YACF;QACF;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAM,KAAK;kBACV,cAAA,8OAAC;;8BACC,8OAAC;8BACC,cAAA,8OAAC;wBACC,QAAO;wBACP,OAAO,YAAY,MAAM;wBACzB,OAAO,IAAI,aAAa,YAAY,OAAO,CAAC,CAAA,IAAK;gCAAC,EAAE,CAAC;gCAAE,EAAE,CAAC;gCAAE,EAAE,CAAC;6BAAC;wBAChE,UAAU;;;;;;;;;;;8BAGd,8OAAC;oBACC,OAAM;oBACN,WAAW;oBACX,SAAS;oBACT,UAAU,+IAAA,CAAA,mBAAsB;;;;;;;;;;;;;;;;;AAK1C;AAEA,4BAA4B;AAC5B,MAAM,uBAAuB;IAC3B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAG;gBAAE,KAAK;YAAG;YACxC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,8OAAC;oBAAa,WAAW;;;;;;8BACzB,8OAAC;oBAAc,OAAO;;;;;;8BACtB,8OAAC;;;;;8BAGD,8OAAC;oBAAI,QAAO;oBAAM,MAAM;wBAAC;wBAAW;wBAAI;qBAAG;;;;;;;;;;;;;;;;;AAInD;uCAEe", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/AITerminal.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Terminal, Play, Zap, FileText, Camera, Bell } from 'lucide-react';\n\nconst AITerminal = () => {\n  const [currentCommand, setCurrentCommand] = useState('');\n  const [output, setOutput] = useState([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showCursor, setShowCursor] = useState(true);\n  const terminalRef = useRef(null);\n\n  const commands = [\n    {\n      command: 'scan receipt.jpg',\n      output: [\n        'Analyzing receipt...',\n        'Product: iPhone 15 Pro',\n        'Brand: Apple',\n        'Purchase Date: 2024-01-15',\n        'Warranty: 1 year (expires 2025-01-15)',\n        '✓ Added to warranty tracker'\n      ],\n      icon: <Camera className=\"w-4 h-4\" />\n    },\n    {\n      command: 'check warranties',\n      output: [\n        'Scanning warranty database...',\n        '📱 iPhone 15 Pro - 11 months remaining',\n        '💻 MacBook Pro - 8 months remaining',\n        '🏠 Samsung Fridge - 2 years remaining',\n        '⚠️  3 items expiring in 30 days'\n      ],\n      icon: <FileText className=\"w-4 h-4\" />\n    },\n    {\n      command: 'set reminder AC filter',\n      output: [\n        'Creating smart reminder...',\n        'Reminder: Replace AC filter',\n        'Frequency: Every 3 months',\n        'Next due: March 15, 2024',\n        '✓ Reminder activated'\n      ],\n      icon: <Bell className=\"w-4 h-4\" />\n    }\n  ];\n\n  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);\n\n  // Cursor blinking effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Auto-demo cycle\n  useEffect(() => {\n    const runDemo = async () => {\n      if (currentCommandIndex >= commands.length) {\n        // Reset after all commands\n        setTimeout(() => {\n          setOutput([]);\n          setCurrentCommandIndex(0);\n        }, 3000);\n        return;\n      }\n\n      const cmd = commands[currentCommandIndex];\n      setIsTyping(true);\n      \n      // Type command\n      for (let i = 0; i <= cmd.command.length; i++) {\n        setCurrentCommand(cmd.command.slice(0, i));\n        await new Promise(resolve => setTimeout(resolve, 50));\n      }\n\n      // Execute command\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setCurrentCommand('');\n      \n      // Show output line by line\n      for (let i = 0; i < cmd.output.length; i++) {\n        await new Promise(resolve => setTimeout(resolve, 300));\n        setOutput(prev => [...prev, {\n          type: 'output',\n          text: cmd.output[i],\n          timestamp: Date.now()\n        }]);\n      }\n\n      setIsTyping(false);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setCurrentCommandIndex(prev => prev + 1);\n    };\n\n    const timer = setTimeout(runDemo, 1000);\n    return () => clearTimeout(timer);\n  }, [currentCommandIndex]);\n\n  const handleCommandClick = (cmdIndex) => {\n    setCurrentCommandIndex(cmdIndex);\n    setOutput([]);\n  };\n\n  return (\n    <div className=\"relative w-full max-w-4xl mx-auto\">\n      {/* Terminal Window */}\n      <div className=\"glass rounded-lg overflow-hidden border border-primary/20 shadow-2xl\">\n        {/* Terminal Header */}\n        <div className=\"flex items-center justify-between px-4 py-3 bg-surface/50 border-b border-primary/20\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex space-x-2\">\n              <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n              <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n            </div>\n            <Terminal className=\"w-4 h-4 text-primary ml-4\" />\n            <span className=\"text-sm font-mono text-white/80\">WarrantyAI Terminal v2.0</span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 rounded-full bg-accent animate-pulse\"></div>\n            <span className=\"text-xs text-accent font-mono\">ONLINE</span>\n          </div>\n        </div>\n\n        {/* Terminal Content */}\n        <div \n          ref={terminalRef}\n          className=\"p-6 h-80 overflow-y-auto font-mono text-sm bg-gradient-to-b from-space/80 to-surface/80\"\n        >\n          {/* Welcome Message */}\n          <div className=\"text-accent mb-4\">\n            <div>Welcome to WarrantyAI Terminal</div>\n            <div className=\"text-white/60\">Type commands or click suggestions below</div>\n            <div className=\"text-primary\">{'>'} Ready for input...</div>\n          </div>\n\n          {/* Output History */}\n          {output.map((line, index) => (\n            <div key={index} className=\"mb-1\">\n              {line.type === 'output' && (\n                <div className=\"text-white/80 pl-4\">{line.text}</div>\n              )}\n            </div>\n          ))}\n\n          {/* Current Command Line */}\n          <div className=\"flex items-center text-primary\">\n            <span className=\"mr-2\">{'>'}</span>\n            <span>{currentCommand}</span>\n            {showCursor && <span className=\"bg-primary w-2 h-4 ml-1 animate-pulse\">|</span>}\n          </div>\n        </div>\n\n        {/* Command Suggestions */}\n        <div className=\"p-4 bg-surface/30 border-t border-primary/20\">\n          <div className=\"text-xs text-white/60 mb-2\">Quick Commands:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {commands.map((cmd, index) => (\n              <button\n                key={index}\n                onClick={() => handleCommandClick(index)}\n                className=\"flex items-center space-x-2 px-3 py-1 bg-primary/10 hover:bg-primary/20 rounded border border-primary/30 transition-all duration-300 text-xs text-white/80 hover:text-white\"\n              >\n                {cmd.icon}\n                <span>{cmd.command}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Buttons */}\n      <div className=\"absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-4\">\n        <button className=\"w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Play className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n        <button className=\"w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center shadow-lg hover:shadow-accent/25 transition-all duration-300 transform hover:scale-110 group\">\n          <Zap className=\"w-5 h-5 text-white group-hover:text-space\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AITerminal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,WAAW;QACf;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,SAAS;YACT,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxB;KACD;IAED,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAA,OAAQ,CAAC;QACzB,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,IAAI,uBAAuB,SAAS,MAAM,EAAE;gBAC1C,2BAA2B;gBAC3B,WAAW;oBACT,UAAU,EAAE;oBACZ,uBAAuB;gBACzB,GAAG;gBACH;YACF;YAEA,MAAM,MAAM,QAAQ,CAAC,oBAAoB;YACzC,YAAY;YAEZ,eAAe;YACf,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,IAAK;gBAC5C,kBAAkB,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG;gBACvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,kBAAkB;YAElB,2BAA2B;YAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,UAAU,CAAA,OAAQ;2BAAI;wBAAM;4BAC1B,MAAM;4BACN,MAAM,IAAI,MAAM,CAAC,EAAE;4BACnB,WAAW,KAAK,GAAG;wBACrB;qBAAE;YACJ;YAEA,YAAY;YACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,uBAAuB,CAAA,OAAQ,OAAO;QACxC;QAEA,MAAM,QAAQ,WAAW,SAAS;QAClC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAoB;IAExB,MAAM,qBAAqB,CAAC;QAC1B,uBAAuB;QACvB,UAAU,EAAE;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAKpD,8OAAC;wBACC,KAAK;wBACL,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;;4CAAgB;4CAAI;;;;;;;;;;;;;4BAIpC,OAAO,GAAG,CAAC,CAAC,MAAM,sBACjB,8OAAC;oCAAgB,WAAU;8CACxB,KAAK,IAAI,KAAK,0BACb,8OAAC;wCAAI,WAAU;kDAAsB,KAAK,IAAI;;;;;;mCAFxC;;;;;0CAQZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;kDAAM;;;;;;oCACN,4BAAc,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAK3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;4CAET,IAAI,IAAI;0DACT,8OAAC;0DAAM,IAAI,OAAO;;;;;;;uCALb;;;;;;;;;;;;;;;;;;;;;;0BAaf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport AIParticleBackground from '../three/AIParticleBackground';\nimport AITerminal from '../ui/AITerminal';\nimport { ArrowDown, Sparkles, Shield, Clock } from 'lucide-react';\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst HeroSection = () => {\n  const heroRef = useRef(null);\n  const titleRef = useRef(null);\n  const subtitleRef = useRef(null);\n  const terminalRef = useRef(null);\n  const floatingElementsRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial animations\n      gsap.set([titleRef.current, subtitleRef.current, terminalRef.current], {\n        opacity: 0,\n        y: 50\n      });\n\n      // Entrance animations\n      const tl = gsap.timeline();\n      \n      tl.to(titleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      })\n      .to(subtitleRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 0.8,\n        ease: \"power3.out\"\n      }, \"-=0.5\")\n      .to(terminalRef.current, {\n        opacity: 1,\n        y: 0,\n        duration: 1,\n        ease: \"power3.out\"\n      }, \"-=0.3\");\n\n      // Floating animations for decorative elements\n      floatingElementsRef.current.forEach((el, index) => {\n        if (el) {\n          gsap.to(el, {\n            y: -20,\n            duration: 2 + index * 0.5,\n            ease: \"power2.inOut\",\n            repeat: -1,\n            yoyo: true,\n            delay: index * 0.3\n          });\n        }\n      });\n\n      // Scroll-triggered morphing\n      ScrollTrigger.create({\n        trigger: heroRef.current,\n        start: \"top top\",\n        end: \"bottom top\",\n        scrub: 1,\n        onUpdate: (self) => {\n          const progress = self.progress;\n          gsap.to(titleRef.current, {\n            scale: 1 - progress * 0.1,\n            opacity: 1 - progress * 0.5,\n            duration: 0.3\n          });\n        }\n      });\n\n    }, heroRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToFloatingRefs = (el) => {\n    if (el && !floatingElementsRef.current.includes(el)) {\n      floatingElementsRef.current.push(el);\n    }\n  };\n\n  return (\n    <section \n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden\"\n    >\n      {/* Three.js Background */}\n      <AIParticleBackground />\n\n      {/* Gradient Overlays */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-space/20 to-space/80\"></div>\n      <div className=\"absolute inset-0 bg-gradient-radial from-primary/5 via-transparent to-transparent\"></div>\n\n      {/* Floating Decorative Elements */}\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-20 left-10 w-16 h-16 border border-primary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Shield className=\"w-8 h-8 text-primary\" />\n      </div>\n      \n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute top-32 right-16 w-12 h-12 border border-accent/30 rounded-lg flex items-center justify-center glass\"\n      >\n        <Clock className=\"w-6 h-6 text-accent\" />\n      </div>\n\n      <div \n        ref={addToFloatingRefs}\n        className=\"absolute bottom-32 left-20 w-20 h-20 border border-secondary/30 rounded-full flex items-center justify-center glass\"\n      >\n        <Sparkles className=\"w-10 h-10 text-secondary\" />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        \n        {/* Hero Title */}\n        <div ref={titleRef} className=\"mb-8\">\n          <h1 className=\"font-heading text-5xl md:text-7xl lg:text-8xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Never Miss\n            </span>\n            <br />\n            <span className=\"text-white text-glow-primary\">\n              A Warranty\n            </span>\n            <br />\n            <span className=\"bg-gradient-to-r from-accent via-primary to-secondary bg-clip-text text-transparent\">\n              Again\n            </span>\n          </h1>\n        </div>\n\n        {/* Hero Subtitle */}\n        <div ref={subtitleRef} className=\"mb-12\">\n          <p className=\"text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed\">\n            Smart AI assistant to track, manage, and remind you of all warranties, \n            services, and coverage across your entire digital and physical world.\n          </p>\n          <div className=\"flex flex-wrap justify-center gap-4 mt-6 text-sm\">\n            <span className=\"px-4 py-2 glass rounded-full text-primary border border-primary/30\">\n              🤖 AI-Powered Extraction\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-accent border border-accent/30\">\n              📱 Smart Reminders\n            </span>\n            <span className=\"px-4 py-2 glass rounded-full text-secondary border border-secondary/30\">\n              🏠 3D Inventory\n            </span>\n          </div>\n        </div>\n\n        {/* AI Terminal Demo */}\n        <div ref={terminalRef} className=\"mb-12\">\n          <AITerminal />\n        </div>\n\n        {/* CTA Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n          <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105 glow-primary\">\n            Start Free Trial\n          </button>\n          <button className=\"border border-primary/50 px-8 py-4 rounded-full text-white font-semibold text-lg hover:bg-primary/10 transition-all duration-300 transform hover:scale-105\">\n            Watch Demo\n          </button>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ArrowDown className=\"w-6 h-6 text-white/60\" />\n        </div>\n      </div>\n\n      {/* Morphing Shapes */}\n      <div className=\"absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute bottom-1/4 right-1/4 w-48 h-48 bg-gradient-to-r from-accent/20 to-primary/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASA,wBAAwB;AACxB,uCAAmC;;AAEnC;AAEA,MAAM,cAAc;IAClB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,qBAAqB;YACrB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC,SAAS,OAAO;gBAAE,YAAY,OAAO;gBAAE,YAAY,OAAO;aAAC,EAAE;gBACrE,SAAS;gBACT,GAAG;YACL;YAEA,sBAAsB;YACtB,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ;YAExB,GAAG,EAAE,CAAC,SAAS,OAAO,EAAE;gBACtB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACR,GACC,EAAE,CAAC,YAAY,OAAO,EAAE;gBACvB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACR,GAAG,SACF,EAAE,CAAC,YAAY,OAAO,EAAE;gBACvB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACR,GAAG;YAEH,8CAA8C;YAC9C,oBAAoB,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI;gBACvC,IAAI,IAAI;oBACN,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,IAAI;wBACV,GAAG,CAAC;wBACJ,UAAU,IAAI,QAAQ;wBACtB,MAAM;wBACN,QAAQ,CAAC;wBACT,MAAM;wBACN,OAAO,QAAQ;oBACjB;gBACF;YACF;YAEA,4BAA4B;YAC5B,qIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBACnB,SAAS,QAAQ,OAAO;gBACxB,OAAO;gBACP,KAAK;gBACL,OAAO;gBACP,UAAU,CAAC;oBACT,MAAM,WAAW,KAAK,QAAQ;oBAC9B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;wBACxB,OAAO,IAAI,WAAW;wBACtB,SAAS,IAAI,WAAW;wBACxB,UAAU;oBACZ;gBACF;YACF;QAEF,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,CAAC,oBAAoB,OAAO,CAAC,QAAQ,CAAC,KAAK;YACnD,oBAAoB,OAAO,CAAC,IAAI,CAAC;QACnC;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC,kJAAA,CAAA,UAAoB;;;;;0BAGrB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,KAAK;wBAAU,WAAU;kCAC5B,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAsF;;;;;;8CAGtG,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAG/C,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAsF;;;;;;;;;;;;;;;;;kCAO1G,8OAAC;wBAAI,KAAK;wBAAa,WAAU;;0CAC/B,8OAAC;gCAAE,WAAU;0CAAsE;;;;;;0CAInF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAqE;;;;;;kDAGrF,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;kDAGnF,8OAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;kCAO7F,8OAAC;wBAAI,KAAK;wBAAa,WAAU;kCAC/B,cAAA,8OAAC,qIAAA,CAAA,UAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAAgN;;;;;;0CAGlO,8OAAC;gCAAO,WAAU;0CAA6J;;;;;;;;;;;;kCAMjL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { AlertTriangle, CheckCircle, Receipt, Clock, Shield, Smartphone } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ProblemSolutionSection = () => {\n  const sectionRef = useRef(null);\n  const problemRef = useRef(null);\n  const solutionRef = useRef(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Split screen animation\n      gsap.set([problemRef.current, solutionRef.current], {\n        x: (index) => index === 0 ? -100 : 100,\n        opacity: 0\n      });\n\n      ScrollTrigger.create({\n        trigger: sectionRef.current,\n        start: \"top 80%\",\n        end: \"bottom 20%\",\n        onEnter: () => {\n          gsap.to([problemRef.current, solutionRef.current], {\n            x: 0,\n            opacity: 1,\n            duration: 1,\n            stagger: 0.2,\n            ease: \"power3.out\"\n          });\n        }\n      });\n\n      // Floating animation for icons\n      gsap.to(\".floating-icon\", {\n        y: -10,\n        duration: 2,\n        ease: \"power2.inOut\",\n        repeat: -1,\n        yoyo: true,\n        stagger: 0.3\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const problems = [\n    {\n      icon: <Receipt className=\"w-8 h-8\" />,\n      title: \"Lost Receipts\",\n      description: \"70% of people lose or misplace important receipts and warranty documents\"\n    },\n    {\n      icon: <Clock className=\"w-8 h-8\" />,\n      title: \"Missed Deadlines\",\n      description: \"Billions in unclaimed warranties due to forgotten expiration dates\"\n    },\n    {\n      icon: <AlertTriangle className=\"w-8 h-8\" />,\n      title: \"Manual Tracking\",\n      description: \"Chaotic spreadsheets and sticky notes for managing multiple warranties\"\n    }\n  ];\n\n  const solutions = [\n    {\n      icon: <Smartphone className=\"w-8 h-8\" />,\n      title: \"AI-Powered Scanning\",\n      description: \"Instantly extract warranty info from receipts, emails, and photos\"\n    },\n    {\n      icon: <Shield className=\"w-8 h-8\" />,\n      title: \"Smart Reminders\",\n      description: \"Never miss a warranty claim or service deadline again\"\n    },\n    {\n      icon: <CheckCircle className=\"w-8 h-8\" />,\n      title: \"Centralized Management\",\n      description: \"All your warranties, services, and coverage in one intelligent dashboard\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">The Problem</span>\n            <span className=\"text-primary mx-4\">→</span>\n            <span className=\"bg-gradient-to-r from-accent to-primary bg-clip-text text-transparent\">\n              Our Solution\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n            Warranty management is broken. We're fixing it with AI.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Problem Side */}\n          <div ref={problemRef} className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"font-heading text-3xl font-bold text-white mb-4\">\n                Current Pain Points\n              </h3>\n              <p className=\"text-white/70 text-lg\">\n                Managing warranties and service schedules is a nightmare for consumers and businesses alike.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {problems.map((problem, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-start space-x-4 p-6 glass rounded-lg border border-red-500/20 hover:border-red-500/40 transition-all duration-300\"\n                >\n                  <div className=\"floating-icon text-red-400 mt-1\">\n                    {problem.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-white text-lg mb-2\">\n                      {problem.title}\n                    </h4>\n                    <p className=\"text-white/70\">\n                      {problem.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Problem Stats */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              <div className=\"text-center p-4 glass rounded-lg border border-red-500/20\">\n                <div className=\"text-2xl font-bold text-red-400\">$2.1B</div>\n                <div className=\"text-sm text-white/60\">Unclaimed Warranties</div>\n              </div>\n              <div className=\"text-center p-4 glass rounded-lg border border-red-500/20\">\n                <div className=\"text-2xl font-bold text-red-400\">70%</div>\n                <div className=\"text-sm text-white/60\">Lost Receipts</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Solution Side */}\n          <div ref={solutionRef} className=\"space-y-8\">\n            <div className=\"text-center lg:text-left\">\n              <h3 className=\"font-heading text-3xl font-bold text-white mb-4\">\n                WarrantyAI Solution\n              </h3>\n              <p className=\"text-white/70 text-lg\">\n                Intelligent automation that transforms how you manage warranties and asset coverage.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {solutions.map((solution, index) => (\n                <div \n                  key={index}\n                  className=\"flex items-start space-x-4 p-6 glass rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300 hover:glow-accent\"\n                >\n                  <div className=\"floating-icon text-accent mt-1\">\n                    {solution.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-white text-lg mb-2\">\n                      {solution.title}\n                    </h4>\n                    <p className=\"text-white/70\">\n                      {solution.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Solution Benefits */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              <div className=\"text-center p-4 glass rounded-lg border border-accent/20\">\n                <div className=\"text-2xl font-bold text-accent\">100%</div>\n                <div className=\"text-sm text-white/60\">Warranty Tracking</div>\n              </div>\n              <div className=\"text-center p-4 glass rounded-lg border border-accent/20\">\n                <div className=\"text-2xl font-bold text-accent\">0</div>\n                <div className=\"text-sm text-white/60\">Missed Claims</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Connecting Arrow */}\n        <div className=\"hidden lg:flex absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse-glow\">\n            <span className=\"text-2xl\">→</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute top-10 left-10 w-32 h-32 bg-gradient-to-r from-red-500/10 to-transparent rounded-full blur-xl\"></div>\n      <div className=\"absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-r from-accent/10 to-transparent rounded-full blur-xl\"></div>\n    </section>\n  );\n};\n\nexport default ProblemSolutionSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,uCAAmC;;AAEnC;AAEA,MAAM,yBAAyB;IAC7B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,yBAAyB;YACzB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC,WAAW,OAAO;gBAAE,YAAY,OAAO;aAAC,EAAE;gBAClD,GAAG,CAAC,QAAU,UAAU,IAAI,CAAC,MAAM;gBACnC,SAAS;YACX;YAEA,qIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBACnB,SAAS,WAAW,OAAO;gBAC3B,OAAO;gBACP,KAAK;gBACL,SAAS;oBACP,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC;wBAAC,WAAW,OAAO;wBAAE,YAAY,OAAO;qBAAC,EAAE;wBACjD,GAAG;wBACH,SAAS;wBACT,UAAU;wBACV,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YAEA,+BAA+B;YAC/B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,kBAAkB;gBACxB,GAAG,CAAC;gBACJ,UAAU;gBACV,MAAM;gBACN,QAAQ,CAAC;gBACT,MAAM;gBACN,SAAS;YACX;QAEF,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YACE,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,8OAAC;wCAAK,WAAU;kDAAwE;;;;;;;;;;;;0CAI1F,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,KAAK;gCAAY,WAAU;;kDAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI;;;;;;kEAEf,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;+CAXnB;;;;;;;;;;kDAmBX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,KAAK;gCAAa,WAAU;;kDAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI;;;;;;kEAEhB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,SAAS,KAAK;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;;;;;;;;+CAXpB;;;;;;;;;;kDAmBX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiC;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/ThreeStepSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { Upload, Brain, Bell, ArrowRight } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ThreeStepSection = () => {\n  const sectionRef = useRef(null);\n  const timelineRef = useRef(null);\n  const stepsRef = useRef([]);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial state\n      gsap.set(stepsRef.current, {\n        opacity: 0,\n        y: 50,\n        scale: 0.8\n      });\n\n      gsap.set(\".step-connector\", {\n        scaleX: 0,\n        transformOrigin: \"left center\"\n      });\n\n      // Timeline animation\n      const tl = gsap.timeline({\n        scrollTrigger: {\n          trigger: sectionRef.current,\n          start: \"top 70%\",\n          end: \"bottom 30%\",\n          toggleActions: \"play none none reverse\"\n        }\n      });\n\n      // Animate steps in sequence\n      stepsRef.current.forEach((step, index) => {\n        tl.to(step, {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.6,\n          ease: \"back.out(1.7)\"\n        }, index * 0.3);\n\n        // Animate connector after step\n        if (index < stepsRef.current.length - 1) {\n          tl.to(`.step-connector-${index}`, {\n            scaleX: 1,\n            duration: 0.4,\n            ease: \"power2.out\"\n          }, index * 0.3 + 0.3);\n        }\n      });\n\n      // Floating animation for icons\n      gsap.to(\".step-icon\", {\n        y: -5,\n        duration: 2,\n        ease: \"power2.inOut\",\n        repeat: -1,\n        yoyo: true,\n        stagger: 0.5\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToStepsRefs = (el) => {\n    if (el && !stepsRef.current.includes(el)) {\n      stepsRef.current.push(el);\n    }\n  };\n\n  const steps = [\n    {\n      number: \"01\",\n      title: \"Upload & Scan\",\n      description: \"Simply upload receipts, photos, or forward emails. Our AI instantly extracts product details, warranty info, and service dates.\",\n      icon: <Upload className=\"w-8 h-8\" />,\n      color: \"primary\",\n      features: [\"Receipt scanning\", \"Email integration\", \"Photo recognition\", \"Bulk upload\"]\n    },\n    {\n      number: \"02\", \n      title: \"AI Processing\",\n      description: \"Advanced machine learning analyzes your items, identifies warranty terms, and creates smart reminders for optimal timing.\",\n      icon: <Brain className=\"w-8 h-8\" />,\n      color: \"secondary\",\n      features: [\"Smart extraction\", \"Warranty detection\", \"Service scheduling\", \"Data validation\"]\n    },\n    {\n      number: \"03\",\n      title: \"Smart Alerts\",\n      description: \"Receive timely notifications before warranties expire, service is due, or claims need to be filed. Never miss another deadline.\",\n      icon: <Bell className=\"w-8 h-8\" />,\n      color: \"accent\",\n      features: [\"Proactive reminders\", \"Custom alerts\", \"Claim assistance\", \"Calendar sync\"]\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">How It Works</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              3 Simple Steps\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n            From chaos to clarity in minutes. Our AI handles the complexity so you don't have to.\n          </p>\n        </div>\n\n        {/* Timeline */}\n        <div ref={timelineRef} className=\"relative\">\n          {/* Desktop Timeline */}\n          <div className=\"hidden lg:flex items-center justify-between mb-16\">\n            {steps.map((step, index) => (\n              <div key={index} className=\"flex items-center\">\n                {/* Step */}\n                <div \n                  ref={addToStepsRefs}\n                  className={`relative z-10 w-32 h-32 rounded-full border-4 border-${step.color} bg-gradient-to-br from-${step.color}/20 to-transparent flex items-center justify-center group hover:scale-110 transition-all duration-300`}\n                >\n                  <div className=\"step-icon text-white group-hover:text-accent transition-colors duration-300\">\n                    {step.icon}\n                  </div>\n                  <div className={`absolute -top-2 -right-2 w-8 h-8 bg-${step.color} rounded-full flex items-center justify-center text-xs font-bold text-space`}>\n                    {step.number}\n                  </div>\n                </div>\n\n                {/* Connector */}\n                {index < steps.length - 1 && (\n                  <div className={`step-connector step-connector-${index} flex-1 h-1 bg-gradient-to-r from-${step.color} to-${steps[index + 1].color} mx-8`}></div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Step Details */}\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {steps.map((step, index) => (\n              <div \n                key={index}\n                ref={addToStepsRefs}\n                className=\"group\"\n              >\n                {/* Mobile Step Icon */}\n                <div className=\"lg:hidden mb-6\">\n                  <div className={`w-20 h-20 rounded-full border-4 border-${step.color} bg-gradient-to-br from-${step.color}/20 to-transparent flex items-center justify-center mx-auto`}>\n                    <div className=\"step-icon text-white\">\n                      {step.icon}\n                    </div>\n                    <div className={`absolute -top-2 -right-2 w-6 h-6 bg-${step.color} rounded-full flex items-center justify-center text-xs font-bold text-space`}>\n                      {step.number}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Content Card */}\n                <div className=\"glass rounded-lg p-6 h-full border border-white/10 hover:border-white/20 transition-all duration-300 group-hover:glow-primary\">\n                  <div className=\"text-center lg:text-left\">\n                    <h3 className=\"font-heading text-2xl font-bold text-white mb-4\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-white/80 mb-6 leading-relaxed\">\n                      {step.description}\n                    </p>\n\n                    {/* Features */}\n                    <div className=\"space-y-2\">\n                      {step.features.map((feature, featureIndex) => (\n                        <div key={featureIndex} className=\"flex items-center space-x-2\">\n                          <div className={`w-2 h-2 rounded-full bg-${step.color}`}></div>\n                          <span className=\"text-sm text-white/70\">{feature}</span>\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* Action Button */}\n                    <button className={`mt-6 w-full bg-gradient-to-r from-${step.color}/20 to-${step.color}/10 border border-${step.color}/30 px-4 py-2 rounded-lg text-white hover:bg-${step.color}/30 transition-all duration-300 flex items-center justify-center space-x-2 group-hover:scale-105`}>\n                      <span>Learn More</span>\n                      <ArrowRight className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <button className=\"bg-gradient-to-r from-primary to-secondary px-8 py-4 rounded-full text-white font-semibold text-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105 glow-primary\">\n            Try It Now - Free\n          </button>\n          <p className=\"text-white/60 mt-4\">No credit card required • 30-day free trial</p>\n        </div>\n      </div>\n\n      {/* Background Decorations */}\n      <div className=\"absolute top-20 left-10 w-24 h-24 bg-gradient-to-r from-primary/20 to-transparent rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-secondary/20 to-transparent rounded-full blur-xl animate-pulse delay-1000\"></div>\n      <div className=\"absolute bottom-20 left-1/3 w-28 h-28 bg-gradient-to-r from-accent/20 to-transparent rounded-full blur-xl animate-pulse delay-2000\"></div>\n    </section>\n  );\n};\n\nexport default ThreeStepSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,uCAAmC;;AAEnC;AAEA,MAAM,mBAAmB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,gBAAgB;YAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;gBACzB,SAAS;gBACT,GAAG;gBACH,OAAO;YACT;YAEA,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,mBAAmB;gBAC1B,QAAQ;gBACR,iBAAiB;YACnB;YAEA,qBAAqB;YACrB,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACvB,eAAe;oBACb,SAAS,WAAW,OAAO;oBAC3B,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAEA,4BAA4B;YAC5B,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;gBAC9B,GAAG,EAAE,CAAC,MAAM;oBACV,SAAS;oBACT,GAAG;oBACH,OAAO;oBACP,UAAU;oBACV,MAAM;gBACR,GAAG,QAAQ;gBAEX,+BAA+B;gBAC/B,IAAI,QAAQ,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;oBACvC,GAAG,EAAE,CAAC,CAAC,gBAAgB,EAAE,OAAO,EAAE;wBAChC,QAAQ;wBACR,UAAU;wBACV,MAAM;oBACR,GAAG,QAAQ,MAAM;gBACnB;YACF;YAEA,+BAA+B;YAC/B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,cAAc;gBACpB,GAAG,CAAC;gBACJ,UAAU;gBACV,MAAM;gBACN,QAAQ,CAAC;gBACT,MAAM;gBACN,SAAS;YACX;QAEF,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK;YACxC,SAAS,OAAO,CAAC,IAAI,CAAC;QACxB;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,UAAU;gBAAC;gBAAoB;gBAAqB;gBAAqB;aAAc;QACzF;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,UAAU;gBAAC;gBAAoB;gBAAsB;gBAAsB;aAAkB;QAC/F;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,UAAU;gBAAC;gBAAuB;gBAAiB;gBAAoB;aAAgB;QACzF;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,KAAK;wBAAa,WAAU;;0CAE/B,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDACC,KAAK;gDACL,WAAW,CAAC,qDAAqD,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,qGAAqG,CAAC;;kEAEzN,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAI,WAAW,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,2EAA2E,CAAC;kEAC3I,KAAK,MAAM;;;;;;;;;;;;4CAKf,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAW,CAAC,8BAA8B,EAAE,MAAM,kCAAkC,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;;;uCAhBnI;;;;;;;;;;0CAuBd,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAEC,KAAK;wCACL,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAW,CAAC,uCAAuC,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,2DAA2D,CAAC;;sEACpK,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAI,WAAW,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,2EAA2E,CAAC;sEAC3I,KAAK,MAAM;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,8OAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;oEAAuB,WAAU;;sFAChC,8OAAC;4EAAI,WAAW,CAAC,wBAAwB,EAAE,KAAK,KAAK,EAAE;;;;;;sFACvD,8OAAC;4EAAK,WAAU;sFAAyB;;;;;;;mEAFjC;;;;;;;;;;sEAQd,8OAAC;4DAAO,WAAW,CAAC,kCAAkC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,6CAA6C,EAAE,KAAK,KAAK,CAAC,gGAAgG,CAAC;;8EAC/Q,8OAAC;8EAAK;;;;;;8EACN,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uCAvCvB;;;;;;;;;;;;;;;;kCAiDb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAAgN;;;;;;0CAGlO,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;0BAKtC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/sections/PricingSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { Check, Star, Zap, Crown, Rocket } from 'lucide-react';\n\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst PricingSection = () => {\n  const sectionRef = useRef(null);\n  const cardsRef = useRef([]);\n  const [billingCycle, setBillingCycle] = useState('monthly');\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Initial state\n      gsap.set(cardsRef.current, {\n        opacity: 0,\n        y: 50,\n        rotationY: 15\n      });\n\n      // Entrance animation\n      ScrollTrigger.create({\n        trigger: sectionRef.current,\n        start: \"top 80%\",\n        onEnter: () => {\n          gsap.to(cardsRef.current, {\n            opacity: 1,\n            y: 0,\n            rotationY: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n          });\n        }\n      });\n\n      // Hover animations\n      cardsRef.current.forEach(card => {\n        if (card) {\n          card.addEventListener('mouseenter', () => {\n            gsap.to(card, {\n              scale: 1.05,\n              rotationY: -5,\n              z: 50,\n              duration: 0.3,\n              ease: \"power2.out\"\n            });\n          });\n\n          card.addEventListener('mouseleave', () => {\n            gsap.to(card, {\n              scale: 1,\n              rotationY: 0,\n              z: 0,\n              duration: 0.3,\n              ease: \"power2.out\"\n            });\n          });\n        }\n      });\n\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const addToCardsRefs = (el) => {\n    if (el && !cardsRef.current.includes(el)) {\n      cardsRef.current.push(el);\n    }\n  };\n\n  const plans = [\n    {\n      name: \"Free\",\n      icon: <Star className=\"w-6 h-6\" />,\n      price: { monthly: 0, yearly: 0 },\n      description: \"Perfect for getting started with warranty tracking\",\n      features: [\n        \"5 items per month\",\n        \"Basic receipt scanning\",\n        \"Simple reminders\",\n        \"Mobile app access\",\n        \"Cloud storage (1GB)\"\n      ],\n      limitations: [\n        \"Limited AI features\",\n        \"Basic support\"\n      ],\n      color: \"white\",\n      popular: false,\n      cta: \"Start Free\"\n    },\n    {\n      name: \"Pro\",\n      icon: <Zap className=\"w-6 h-6\" />,\n      price: { monthly: 9.99, yearly: 99.99 },\n      description: \"For individuals and families who want full control\",\n      features: [\n        \"Unlimited items\",\n        \"Advanced AI extraction\",\n        \"Smart reminders\",\n        \"Email integration\",\n        \"Calendar sync\",\n        \"Priority support\",\n        \"Cloud storage (10GB)\",\n        \"Export capabilities\"\n      ],\n      limitations: [],\n      color: \"primary\",\n      popular: true,\n      cta: \"Start Pro Trial\"\n    },\n    {\n      name: \"Premium\",\n      icon: <Crown className=\"w-6 h-6\" />,\n      price: { monthly: 19.99, yearly: 199.99 },\n      description: \"Advanced features for power users and families\",\n      features: [\n        \"Everything in Pro\",\n        \"3D/AR inventory view\",\n        \"Advanced analytics\",\n        \"Family sharing (5 users)\",\n        \"Custom categories\",\n        \"API access\",\n        \"White-label options\",\n        \"Cloud storage (100GB)\",\n        \"Phone support\"\n      ],\n      limitations: [],\n      color: \"secondary\",\n      popular: false,\n      cta: \"Start Premium\"\n    },\n    {\n      name: \"Business\",\n      icon: <Rocket className=\"w-6 h-6\" />,\n      price: { monthly: 99, yearly: 999 },\n      description: \"For businesses managing multiple assets and teams\",\n      features: [\n        \"Everything in Premium\",\n        \"Unlimited team members\",\n        \"Advanced reporting\",\n        \"Custom integrations\",\n        \"Dedicated support\",\n        \"SLA guarantee\",\n        \"Custom training\",\n        \"Unlimited storage\",\n        \"On-premise option\"\n      ],\n      limitations: [],\n      color: \"accent\",\n      popular: false,\n      cta: \"Contact Sales\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"font-heading text-4xl md:text-5xl font-bold mb-6\">\n            <span className=\"text-white\">Simple, Transparent</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent\">\n              Pricing\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/80 max-w-3xl mx-auto mb-8\">\n            Choose the plan that fits your needs. Start free, upgrade anytime.\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center space-x-4\">\n            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-white' : 'text-white/60'}`}>\n              Monthly\n            </span>\n            <button\n              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}\n              className=\"relative w-14 h-7 bg-surface rounded-full border border-primary/30 transition-all duration-300\"\n            >\n              <div className={`absolute top-1 w-5 h-5 bg-primary rounded-full transition-all duration-300 ${\n                billingCycle === 'yearly' ? 'left-8' : 'left-1'\n              }`}></div>\n            </button>\n            <span className={`text-sm ${billingCycle === 'yearly' ? 'text-white' : 'text-white/60'}`}>\n              Yearly\n            </span>\n            {billingCycle === 'yearly' && (\n              <span className=\"text-xs bg-accent/20 text-accent px-2 py-1 rounded-full\">\n                Save 17%\n              </span>\n            )}\n          </div>\n        </div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid lg:grid-cols-4 gap-6\">\n          {plans.map((plan, index) => (\n            <div\n              key={index}\n              ref={addToCardsRefs}\n              className={`relative group perspective-1000 ${plan.popular ? 'lg:-mt-4' : ''}`}\n            >\n              {/* Popular Badge */}\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 z-10\">\n                  <div className=\"bg-gradient-to-r from-primary to-secondary px-4 py-1 rounded-full text-white text-sm font-semibold\">\n                    Most Popular\n                  </div>\n                </div>\n              )}\n\n              {/* Card */}\n              <div className={`glass rounded-lg p-6 h-full border-2 transition-all duration-300 ${\n                plan.popular \n                  ? 'border-primary glow-primary' \n                  : `border-${plan.color}/20 hover:border-${plan.color}/40`\n              } ${plan.popular ? 'lg:py-8' : ''}`}>\n                \n                {/* Header */}\n                <div className=\"text-center mb-6\">\n                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-br from-${plan.color}/20 to-transparent border border-${plan.color}/30 flex items-center justify-center mx-auto mb-4`}>\n                    <div className={`text-${plan.color === 'white' ? 'white' : plan.color}`}>\n                      {plan.icon}\n                    </div>\n                  </div>\n                  <h3 className=\"font-heading text-xl font-bold text-white mb-2\">\n                    {plan.name}\n                  </h3>\n                  <p className=\"text-white/70 text-sm\">\n                    {plan.description}\n                  </p>\n                </div>\n\n                {/* Price */}\n                <div className=\"text-center mb-6\">\n                  <div className=\"flex items-baseline justify-center\">\n                    <span className=\"text-3xl font-bold text-white\">\n                      ${plan.price[billingCycle]}\n                    </span>\n                    <span className=\"text-white/60 ml-1\">\n                      /{billingCycle === 'monthly' ? 'mo' : 'yr'}\n                    </span>\n                  </div>\n                  {billingCycle === 'yearly' && plan.price.monthly > 0 && (\n                    <div className=\"text-sm text-white/60 mt-1\">\n                      ${(plan.price.yearly / 12).toFixed(2)}/month billed yearly\n                    </div>\n                  )}\n                </div>\n\n                {/* Features */}\n                <div className=\"space-y-3 mb-6\">\n                  {plan.features.map((feature, featureIndex) => (\n                    <div key={featureIndex} className=\"flex items-start space-x-3\">\n                      <Check className={`w-4 h-4 mt-0.5 text-${plan.color === 'white' ? 'accent' : plan.color} flex-shrink-0`} />\n                      <span className=\"text-white/80 text-sm\">{feature}</span>\n                    </div>\n                  ))}\n                  {plan.limitations.map((limitation, limitIndex) => (\n                    <div key={limitIndex} className=\"flex items-start space-x-3 opacity-60\">\n                      <div className=\"w-4 h-4 mt-0.5 border border-white/30 rounded-sm flex-shrink-0\"></div>\n                      <span className=\"text-white/60 text-sm\">{limitation}</span>\n                    </div>\n                  ))}\n                </div>\n\n                {/* CTA Button */}\n                <button className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 ${\n                  plan.popular\n                    ? 'bg-gradient-to-r from-primary to-secondary text-white hover:shadow-lg hover:shadow-primary/25 glow-primary'\n                    : plan.color === 'white'\n                    ? 'border border-white/30 text-white hover:bg-white/10'\n                    : `bg-gradient-to-r from-${plan.color}/20 to-${plan.color}/10 border border-${plan.color}/30 text-white hover:bg-${plan.color}/30`\n                }`}>\n                  {plan.cta}\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom Features */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"font-heading text-2xl font-bold text-white mb-8\">\n            All plans include:\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">30-day free trial</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">Cancel anytime</span>\n            </div>\n            <div className=\"flex items-center justify-center space-x-3\">\n              <Check className=\"w-5 h-5 text-accent\" />\n              <span className=\"text-white/80\">24/7 customer support</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-xl animate-pulse\"></div>\n      <div className=\"absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary/10 to-transparent rounded-full blur-xl animate-pulse delay-1000\"></div>\n    </section>\n  );\n};\n\nexport default PricingSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,uCAAmC;;AAEnC;AAEA,MAAM,iBAAiB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,gBAAgB;YAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;gBACzB,SAAS;gBACT,GAAG;gBACH,WAAW;YACb;YAEA,qBAAqB;YACrB,qIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBACnB,SAAS,WAAW,OAAO;gBAC3B,OAAO;gBACP,SAAS;oBACP,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE;wBACxB,SAAS;wBACT,GAAG;wBACH,WAAW;wBACX,UAAU;wBACV,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YAEA,mBAAmB;YACnB,SAAS,OAAO,CAAC,OAAO,CAAC,CAAA;gBACvB,IAAI,MAAM;oBACR,KAAK,gBAAgB,CAAC,cAAc;wBAClC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;4BACZ,OAAO;4BACP,WAAW,CAAC;4BACZ,GAAG;4BACH,UAAU;4BACV,MAAM;wBACR;oBACF;oBAEA,KAAK,gBAAgB,CAAC,cAAc;wBAClC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;4BACZ,OAAO;4BACP,WAAW;4BACX,GAAG;4BACH,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;QAEF,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK;YACxC,SAAS,OAAO,CAAC,IAAI,CAAC;QACxB;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;gBAAE,SAAS;gBAAG,QAAQ;YAAE;YAC/B,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;gBAAE,SAAS;gBAAM,QAAQ;YAAM;YACtC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAO;YACxC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;gBAAE,SAAS;gBAAI,QAAQ;YAAI;YAClC,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa,EAAE;YACf,OAAO;YACP,SAAS;YACT,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAK5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,YAAY,eAAe,iBAAiB;kDAAE;;;;;;kDAG3F,8OAAC;wCACC,SAAS,IAAM,gBAAgB,iBAAiB,YAAY,WAAW;wCACvE,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAW,CAAC,2EAA2E,EAC1F,iBAAiB,WAAW,WAAW,UACvC;;;;;;;;;;;kDAEJ,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,WAAW,eAAe,iBAAiB;kDAAE;;;;;;oCAGzF,iBAAiB,0BAChB,8OAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;;;;;;;kCAQhF,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAEC,KAAK;gCACL,WAAW,CAAC,gCAAgC,EAAE,KAAK,OAAO,GAAG,aAAa,IAAI;;oCAG7E,KAAK,OAAO,kBACX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAqG;;;;;;;;;;;kDAOxH,8OAAC;wCAAI,WAAW,CAAC,iEAAiE,EAChF,KAAK,OAAO,GACR,gCACA,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAC5D,CAAC,EAAE,KAAK,OAAO,GAAG,YAAY,IAAI;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,4CAA4C,EAAE,KAAK,KAAK,CAAC,iCAAiC,EAAE,KAAK,KAAK,CAAC,iDAAiD,CAAC;kEACxK,cAAA,8OAAC;4DAAI,WAAW,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,UAAU,UAAU,KAAK,KAAK,EAAE;sEACpE,KAAK,IAAI;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAgC;oEAC5C,KAAK,KAAK,CAAC,aAAa;;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;;oEAAqB;oEACjC,iBAAiB,YAAY,OAAO;;;;;;;;;;;;;oDAGzC,iBAAiB,YAAY,KAAK,KAAK,CAAC,OAAO,GAAG,mBACjD,8OAAC;wDAAI,WAAU;;4DAA6B;4DACxC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAM5C,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;4DAAuB,WAAU;;8EAChC,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,KAAK,UAAU,WAAW,KAAK,KAAK,CAAC,cAAc,CAAC;;;;;;8EACvG,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;;2DAFjC;;;;;oDAKX,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,2BACjC,8OAAC;4DAAqB,WAAU;;8EAC9B,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;;2DAFjC;;;;;;;;;;;0DAQd,8OAAC;gDAAO,WAAW,CAAC,2FAA2F,EAC7G,KAAK,OAAO,GACR,+GACA,KAAK,KAAK,KAAK,UACf,wDACA,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,EACpI;0DACC,KAAK,GAAG;;;;;;;;;;;;;+BA5ER;;;;;;;;;;kCAoFX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 2912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i5-d2-warrantyai/src/components/ui/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Zap, Mail, Phone, MapPin, Twitter, Github, Linkedin, Instagram } from 'lucide-react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n      { name: 'API', href: '/api' }\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Contact', href: '/contact' }\n    ],\n    resources: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Tutorials', href: '/tutorials' },\n      { name: 'Community', href: '/community' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: <Twitter className=\"w-5 h-5\" />, href: '#' },\n    { name: 'GitHub', icon: <Github className=\"w-5 h-5\" />, href: '#' },\n    { name: 'LinkedIn', icon: <Linkedin className=\"w-5 h-5\" />, href: '#' },\n    { name: 'Instagram', icon: <Instagram className=\"w-5 h-5\" />, href: '#' }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-surface to-space border-t border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"relative\">\n                  <Zap className=\"w-8 h-8 text-primary\" />\n                  <div className=\"absolute inset-0 w-8 h-8 bg-primary/20 rounded-full blur-md\"></div>\n                </div>\n                <span className=\"font-heading text-xl font-bold text-white\">\n                  WarrantyAI\n                </span>\n              </Link>\n              \n              <p className=\"text-white/70 mb-6 leading-relaxed\">\n                Smart AI assistant to track, manage, and remind you of all warranties, \n                services, and coverage across your entire digital and physical world.\n              </p>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Mail className=\"w-4 h-4\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <Phone className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-white/60\">\n                  <MapPin className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">San Francisco, CA</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    className=\"w-10 h-10 bg-white/5 hover:bg-primary/20 border border-white/10 hover:border-primary/30 rounded-lg flex items-center justify-center text-white/60 hover:text-primary transition-all duration-300 group\"\n                    aria-label={social.name}\n                  >\n                    <div className=\"group-hover:scale-110 transition-transform duration-300\">\n                      {social.icon}\n                    </div>\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Resources Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-4\">Legal</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-white/60 hover:text-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div>\n              <h3 className=\"font-heading text-white font-semibold mb-2\">\n                Stay updated with WarrantyAI\n              </h3>\n              <p className=\"text-white/60 text-sm\">\n                Get the latest features, tips, and warranty management insights.\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"px-4 py-2 bg-white/5 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-primary transition-colors duration-300 w-64\"\n              />\n              <button className=\"bg-gradient-to-r from-primary to-secondary px-6 py-2 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-6 border-t border-white/10\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\n            <div className=\"text-white/60 text-sm\">\n              © {currentYear} WarrantyAI. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-white/60\">\n              <span>Made with ❤️ for warranty peace of mind</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n                <span>All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary to-transparent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACpE;YAAE,MAAM;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QAClE;YAAE,MAAM;YAAY,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;QACtE;YAAE,MAAM;YAAa,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAI;KACzE;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAK9D,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAMlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,IAAI;8DAEvB,cAAA,8OAAC;wDAAI,WAAU;kEACZ,OAAO,IAAI;;;;;;mDANT,OAAO,IAAI;;;;;;;;;;;;;;;;8CAcxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;8CAaxB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,8OAAC;4CAAG,WAAU;sDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CAAO,WAAU;sDAAuL;;;;;;;;;;;;;;;;;;;;;;;kCAQ/M,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}]}